<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Registration | GigGenius</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        html {
            font-size: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        /* Body Styles */
        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            line-height: 1.6;
        }

        /* Logo Styles */
        .logo-container {
            display: flex;
            justify-content: center;
            padding: 0;
        }

        .logo-container a {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo-container img {
            width: 100px;
            height: auto;
        }

        .header h1 {
            color: var(--primary-blue);
            font-size: 2.3rem;
            text-align: center;
            margin-top: 0.5rem;
        }

        .header p {
            color: var(--primary-pink);
            font-size: 1.5rem;
        }

        /* Container Styles */
        .container {
            max-width: 1000px;
            width: 100%;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .subtitle {
            color: var(--primary-pink);
            font-size: 1.1rem;
        }

        .profile-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .profile-photo {
            width: 180px;
            height: 180px;
            margin: 0 auto;
            border-radius: 50%;
            background: var(--background-light);
            border: 2px dashed var(--primary-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .profile-photo:hover {
            border-color: var(--primary-pink);
            background: #f0f7ff;
        }

        .profile-photo span {
            color: var(--primary-blue);
            font-size: 0.9rem;
            font-weight: 500;
            padding: 0 20px;
            text-align: center;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-dark);
            font-weight: 500;
            font-size: 0.95rem;
        }

        input, select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--primary-blue);
            background-color: #f8f9fa;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            color: var(--text-dark);
        }

        input:hover, select:hover {
            border-color: var(--primary-pink);
        }

        input:focus, select:focus {
            border-color: var(--primary-pink);
            outline: none;
            box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
            background-color: white;
        }

        .error-container {
            background-color: #fee2e2;
            border: 1px solid red;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }

        .error-message {
            color: red;
            font-size: 0.875rem;
            margin-top: 5px;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .error-message strong {
            display: block;
            margin-bottom: 4px;
        }

        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 5px;
            margin: 0;
        }

        .checkbox-group input[type="checkbox"] {
            appearance: none;
            -webkit-appearance: none;
            width: 15px;
            height: 15px;
            border: 2px solid var(--primary-blue);
            border-radius: 4px;
            cursor: pointer;
            position: relative;
            margin: 0;
            transition: all 0.2s ease;
        }

        .checkbox-group input[type="checkbox"]:checked {
            background-color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .checkbox-group input[type="checkbox"]:checked::after {
            content: '✓';
            position: absolute;
            color: white;
            font-size: 15px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .checkbox-group input[type="checkbox"]:hover {
            border-color: var(--primary-pink);
        }

        .checkbox-group label {
            flex: 1;
            font-size: 0.95rem;
            line-height: 1.5;
            cursor: pointer;
            color: var(--text-dark);
            padding-top: 2px;
        }

        .checkbox-group a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .checkbox-group a:hover {
            color: var(--primary-pink);
            text-decoration: underline;
        }

        /* Style for required checkbox validation */
        .checkbox-group.error input[type="checkbox"] {
            border-color: #dc3545;
            animation: shake 0.5s linear;
        }

        @keyframes shake {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            75% { transform: translateX(-5px); }
            100% { transform: translateX(0); }
        }

        .checkbox-group input[type="checkbox"]:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
        }

        .full-width {
            grid-column: 1 / -1; /* Makes the element span full width */
        }

        textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--primary-blue);
            background-color: #f8f9fa;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            color: var(--text-dark);
            resize: vertical;
            min-height: 120px;
            font-family: inherit;
        }

        textarea:hover {
            border-color: var(--primary-pink);
        }

        textarea:focus {
            border-color: var(--primary-pink);
            outline: none;
            box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
            background-color: white;
        }

        .char-counter {
            font-size: 0.8rem;
            color: #666;
            text-align: right;
            margin-top: 4px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            align-items: center;
            justify-content: center;
            overflow-y: auto;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background-color: #fff;
            border-radius: 8px;
            max-width: 1600px;
            width: 95%;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            padding: 2rem;
            position: relative;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Additional styles for the success message */
        .success-icon {
            font-size: 64px;
            color: #28a745;
            margin-bottom: 30px;
            animation: scaleIn 0.5s ease-in-out;
        }

        @keyframes scaleIn {
            0% { transform: scale(0); }
            60% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .modal h2 {
            font-size: 28px;
            margin-bottom: 20px;
            color: var(--primary-blue);
        }

        .modal p {
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.5;
            color: var(--text-dark);
            padding: 0 20px;
        }

        .text-center {
            text-align: center;
        }

        .photo-options {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 20px;
        }

        .photo-options button {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            background-color: var(--primary-blue);
            color: white;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            transition: background-color 0.3s;
        }

        .photo-options button:hover {
            background-color: var(--primary-pink);
        }

        .photo-options i {
            font-size: 24px;
        }

        #cameraFeed {
            width: 100%;
            max-width: 640px;
            margin: 0 auto;
            display: block;
        }

        .camera-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
        }

        .camera-controls button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background-color: var(--primary-blue);
            color: white;
            cursor: pointer;
        }

        .close {
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .upload-group {
            margin: 20px 0;
        }

        .upload-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .id-upload-container {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
        }

        .id-upload-box {
            flex: 1;
            position: relative;
            height: 200px;
            border: 1px solid var(--primary-blue);
            background-color: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .id-upload-box:hover {
            border-color: var(--primary-pink);
            background-color: #f0f0f0;
        }

        .id-preview {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1;
        }

        .id-preview i {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .id-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: none;
        }

        .id-upload-box input[type="file"] {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            opacity: 0;
            cursor: pointer;
            z-index: 2;
        }

        .upload-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px;
            text-align: center;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .id-upload-box:hover .upload-overlay {
            transform: translateY(0);
        }

        .upload-requirements {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .upload-requirements small {
            color: #6c757d;
            font-size: 0.875rem;
        }

        .upload-error {
            border-color: #dc3545;
            animation: shake 0.5s linear;
        }

        @keyframes shake {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            75% { transform: translateX(-5px); }
            100% { transform: translateX(0); }
        }
        #successModal .modal-content {
        text-align: center;
        padding: 30px;
        max-width: 400px;
    }

    #successModal .fas.fa-check-circle {
        animation: scaleIn 0.5s ease-in-out;
    }

    @keyframes scaleIn {
        0% { transform: scale(0); }
        60% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    .text-center {
        text-align: center;
    }

    #successModal button {
        margin-top: 20px;
        padding: 10px 25px;
        background-color: var(--primary-blue);
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    #successModal button:hover {
        background-color: var(--primary-pink);
    }

    .steps-container {
        display: flex;
        justify-content: center;
        gap: 40px;
        margin-bottom: 40px;
    }

    .step {
        display: flex;
        align-items: center;
        gap: 10px;
        opacity: 0.5;
        transition: all 0.3s ease;
    }

    .step.active {
        opacity: 1;
    }

    .step-number {
        width: 30px;
        height: 30px;
        background: var(--primary-blue);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
    }

    .step.active .step-number {
        background: var(--primary-pink);
    }

    .step-text {
        font-weight: 500;
        color: var(--text-dark);
    }

    .form-section {
        display: block;
        transition: all 0.3s ease;
    }

    .form-section.hidden {
        display: none;
    }

    .button-container {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 20px;
    }

    .btn {
        padding: 0 20px;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 50px;
    }

    .btn-primary {
        background: var(--primary-pink);
        border: 2px solid var(--primary-pink);
        color: var(--text-light);
    }

    .btn-outline {
        background: white;
        border: 2px solid var(--primary-blue);
        color: var(--primary-blue);
    }

        .btn-gigs {
            background: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            color: var(--text-light);
            width: 200px;
        }

        .btn-primary:hover {
            background: white;
            border: 2px solid var(--primary-pink);
            color: var(--primary-pink);
            text-decoration: none;
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: var(--text-light);
            text-decoration: none;
        }

        .btn-gigs:hover {
            background: white;
            color: var(--primary-blue);
        }


    .mobile-input-group {
        display: flex;
        align-items: center;
        border: 1px solid var(--primary-blue);
        border-radius: 8px;
        overflow: hidden;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .mobile-input-group:hover {
        border-color: var(--primary-pink);
    }

    .mobile-input-group:focus-within {
        border-color: var(--primary-pink);
        outline: none;
        box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
        background-color: white;
    }

    .country-code {
        background-color: transparent;
        padding: 12px 16px;
        border-right: 1px solid var(--primary-blue);
        color: var(--text-dark);
        font-size: 1rem;
        white-space: nowrap;
    }

    .mobile-input-group input {
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        background-color: transparent !important;
    }

    .mobile-input-group.error {
        border-color: #dc3545;
    }

    .password-input-group {
        position: relative;
        display: flex;
        align-items: center;
    }

    .password-input-group input {
        width: 100%;
    }

    .password-toggle {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #6c757d;
        padding: 5px;
        z-index: 2;
    }

    .password-toggle:hover {
        color: var(--primary-blue);
    }

    .required {
        color: red;
        margin-left: 2px;
    }

        #validation-errors ul, #validation-errors-step2 ul{
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        #validation-errors li, #validation-errors-step2 li{
            color: red;
            display: flex;
            justify-content: left;
            align-items: center;
            margin-bottom: 5px;
        }
        #validation-errors li::before, #validation-errors-step2 li::before{
            content: "•";
            margin-right: 10px;
        }
    .loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        z-index: 9999;
        justify-content: center;
        align-items: center;
    }

    .loading-overlay.show {
        display: flex;
    }

    .loading-spinner {
        text-align: center;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid var(--primary-blue);
        border-radius: 50%;
        margin: 0 auto 15px;
        animation: spin 1s linear infinite;
    }

    .loading-spinner p {
        color: var(--primary-blue);
        font-weight: 500;
        margin-top: 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @media screen and (max-width: 360px) {
        /* Container and Layout */
        .container {
            padding: 10px;
        }

        .form-section {
            padding: 15px;
        }

        .form-grid {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        /* Header */
        .header h1 {
            font-size: 24px;
        }

        .header p {
            font-size: 16px;
        }

        /* Profile Section */
        .profile-photo {
            width: 100px;
            height: 100px;
            margin: 0 auto;
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 8px;
            font-size: 14px;
            width: 100%;
        }

        /* Mobile Number Input */
        .mobile-input-group {
            display: flex;
            gap: 8px;
        }

        .country-code {
            width: 80px;
            font-size: 14px;
        }

        /* Buttons */
        .button-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 20px;
        }

        .button-container button {
            width: 100%;
            padding: 12px;
            font-size: 14px;
        }

        /* File Upload */
        .id-upload-box {
            padding: 10px;
            margin-top: 5px;
        }

        /* Helper Text */
        .char-counter,
        .error-message {
            font-size: 12px;
        }

        /* Checkbox */
        .checkbox-group {
            margin: 10px 0;
        }

        .checkbox-group label {
            font-size: 13px;
        }

        /* Step indicators styling for mobile */
        .step {
            text-align: center;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .step-number {
            width: 24px;
            height: 24px;
            font-size: 14px;
            line-height: 24px;
        }

        .step-text {
            text-align: center;
            font-size: 12px;
            display: block;
            width: 100%;
        }

        .steps-container {
            gap: 3rem;
        }
    }

    /* PWD Section Styles */
    .pwd-details-section {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-top: 15px;
        transition: all 0.3s ease;
    }

    .pwd-details-section .form-text {
        color: #6c757d;
        font-size: 12px;
        margin-top: 5px;
    }

    .pwd-details-section .form-text i {
        margin-right: 5px;
        color: #28a745;
    }

    .char-counter {
        font-size: 12px;
        color: #666;
        text-align: right;
        margin-top: 5px;
    }
    </style>
</head>
    <div id="successModal" class="modal">
        <div class="modal-content">
            <div class="text-center">
                <i class="fas fa-check-circle success-icon"></i>
                <h2>Registration Successful!</h2>
                <p>Thank you for registering as a Client. We will review your application and notify you via email once your account has been approved.</p>
                <a href="{{ url_for('landing_page') }}" class="btn btn-gigs">Return to Home</a>
            </div>
        </div>
    </div>
<body>
    <div class="container">
        <div class="header">
            <!-- Logo at the top -->
            <div class="logo-container">
                <a href="{{ url_for('landing_page') }}">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                </a>
            </div>
            <!-- Heading below the logo -->
            <h1>Join as a Client</h1>
            <p class="subtitle">Start your journey with GigGenius</p>
        </div>

        <!-- Progress Steps -->
        <div class="steps-container">
            <div class="step active" id="step1">
                <span class="step-number">1</span>
                <span class="step-text">Personal Info</span>
            </div>
            <div class="step" id="step2">
                <span class="step-number">2</span>
                <span class="step-text">Professional Info</span>
            </div>
        </div>

        <form id="registrationForm" method="POST" enctype="multipart/form-data">
            <!-- Step 1: Personal Information -->
            <div class="form-section" id="section1">
                <div class="profile-section">
                    <div class="profile-photo" onclick="document.getElementById('profilePhoto').click()">
                        <span id="photoPrompt">+ Add Profile Photo</span>
                        <input type="file" id="profilePhoto" name="profilePhoto" accept="image/*" hidden>
                        <img id="photoPreview" style="width: 100%; height: 100%; object-fit: cover; display: none;">
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="firstName">First Name <span class="required">*</span></label>
                        <input type="text" id="firstName" name="firstName" required>
                    </div>

                    <div class="form-group">
                        <label for="lastName">Last Name <span class="required">*</span></label>
                        <input type="text" id="lastName" name="lastName" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Work Email Address <span class="required">*</span></label>
                        <input type="email" id="email" name="email" class="form-control" required>
                        <div id="email-error" class="error-message" style="display: none;"></div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password <span class="required">*</span></label>
                        <div class="password-input-group">
                            <input type="password" id="password" name="password" required>
                            <i class="fas fa-eye-slash password-toggle" id="togglePassword"></i>
                        </div>
                        <div class="error-message" id="password-error" style="display: none;"></div>
                    </div>

                    <div class="form-group">
                        <label for="birthday">Birthday <span class="required">*</span></label>
                        <input type="date" id="birthday" name="birthday" required>
                    </div>

                    <div class="form-group">
                        <label for="country">Country <span class="required">*</span></label>
                        <select id="country" name="country">
                            <option value="">Select your country</option>
                            <option value="Philippines">Philippines</option>
                            <option value="United States">United States</option>
                            <option value="United Kingdom">United Kingdom</option>
                            <option value="Canada">Canada</option>
                            <option value="Australia">Australia</option>
                            <option value="Singapore">Singapore</option>
                            <option value="Japan">Japan</option>
                            <option value="South Korea">South Korea</option>
                            <option value="India">India</option>
                            <option value="Malaysia">Malaysia</option>
                            <option value="Indonesia">Indonesia</option>
                            <option value="Thailand">Thailand</option>
                            <option value="Vietnam">Vietnam</option>
                            <option value="New Zealand">New Zealand</option>
                            <option value="Germany">Germany</option>
                            <option value="France">France</option>
                            <option value="Italy">Italy</option>
                            <option value="Spain">Spain</option>
                            <option value="Netherlands">Netherlands</option>
                            <option value="Sweden">Sweden</option>
                            <option value="Norway">Norway</option>
                            <option value="Denmark">Denmark</option>
                            <option value="Finland">Finland</option>
                            <option value="Ireland">Ireland</option>
                            <option value="Switzerland">Switzerland</option>
                            <option value="Belgium">Belgium</option>
                            <option value="Austria">Austria</option>
                            <option value="Portugal">Portugal</option>
                            <option value="Greece">Greece</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="mobile">Mobile Number <span class="required">*</span></label>
                        <div class="mobile-input-group">
                            <span class="country-code" id="selectedCountryCode">+63</span>
                            <input type="tel" id="mobile" name="mobile" pattern="[0-9]*" inputmode="numeric">
                        </div>
                        <div class="error-message" id="mobile-error" style="display: none;">
                            Mobile number should contain numbers only and be at least 10 digits
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="position">Job Title<span class="required">*</span></label>
                        <input type="text" id="position" name="position" required>
                    </div>

                    <!-- PWD Section -->
                    <div class="form-group full-width">
                        <div class="checkbox-group">
                            <input type="checkbox" id="isPwd" name="is_pwd" value="1">
                            <label for="isPwd">
                                Are you a Person with Disability (PWD)?
                            </label>
                        </div>
                    </div>

                    <!-- PWD Details Section (Hidden by default) -->
                    <div id="pwdDetailsSection" class="pwd-details-section" style="display: none;">
                        <div class="form-group full-width">
                            <label for="pwdCondition">Please describe your condition <span class="required">*</span></label>
                            <textarea
                                id="pwdCondition"
                                name="pwd_condition"
                                maxlength="500"
                                placeholder="Please provide details about your condition (this information is confidential and for internal verification only)"
                            ></textarea>
                            <div id="pwdConditionCounter" class="char-counter"></div>
                            <small class="form-text text-muted">
                                <i class="fas fa-lock"></i> This information is strictly confidential and used only for internal verification purposes.
                            </small>
                        </div>

                        <div class="form-group full-width">
                            <label for="pwdProof">Upload PWD Proof/Documentation <span class="required">*</span></label>
                            <div class="id-upload-box" onclick="document.getElementById('pwdProof').click()">
                                <input type="file"
                                    id="pwdProof"
                                    name="pwdProof"
                                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                    hidden>
                                <div class="id-preview" id="pwdProofPreview">
                                    <i class="fas fa-file-medical"></i>
                                    <span>Click to Upload PWD Documentation</span>
                                </div>
                            </div>
                            <div class="error-message" id="pwdProof-error" style="display: none;"></div>
                            <small class="form-text text-muted">
                                Accepted formats: PDF, Word documents, or images (JPG, PNG). Max size: 5MB
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Error messages container -->
                <div id="validation-errors" class="error-container" style="display: none;"></div>

                <!-- Button container with back and next buttons -->
                <div class="button-container">
                    <button type="button" onclick="goBack()" class="btn btn-outline">Back</button>
                    <button type="button" onclick="validateStep1()" class="btn btn-primary">Next</button>
                </div>
            </div>

            <!-- Business Details Section -->
            <div class="form-section hidden" id="section2">
                <div class="form-group">
                    <div class="profile-photo" onclick="document.getElementById('businessLogo').click()">
                        <span id="businessLogoPrompt">+ Add Business Logo</span>
                        <input type="file" id="businessLogo" name="businessLogo" accept="image/*" hidden>
                        <img id="businessLogoPreview" style="width: 100%; height: 100%; object-fit: cover; display: none;">
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="businessName">Business Name <span class="required">*</span></label>
                        <input type="text" id="businessName" name="businessName" required>
                    </div>

                    <div class="form-group">
                        <label for="businessAddress">Business Address <span class="required">*</span></label>
                        <input type="text" id="businessAddress" name="businessAddress" required>
                    </div>

                    <div class="form-group">
                        <label for="businessEmail">Business Email <span class="required">*</span></label>
                        <input type="email" id="businessEmail" name="businessEmail" required>
                        <div class="error-message" id="businessEmail-error" style="display: none;"></div>
                    </div>

                    <div class="form-group">
                        <label for="industry">Industry <span class="required">*</span></label>
                        <select id="industry" name="industry" required>
                            <option value="">Select your industry</option>
                            <option value="technology">Technology</option>
                            <option value="finance">Finance</option>
                            <option value="healthcare">Healthcare</option>
                            <option value="education">Education</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="businessWebsite">Business Website<span style ="color: red;"> (if any)</span></label>
                        <input type="text" id="businessWebsite" name="businessWebsite">
                    </div>

                    <div class="form-group">
                        <label for="employeeCount">Number of Employees <span class="required">*</span></label>
                        <select id="employeeCount" name="employeeCount" required>
                            <option value="">Select range</option>
                            <option value="1-10">1-10 employees</option>
                            <option value="11-50">11-50 employees</option>
                            <option value="51-200">51-200 employees</option>
                            <option value="201-500">201-500 employees</option>
                            <option value="501-1000">501-1000 employees</option>
                            <option value="1000+">More than 1000 employees</option>
                        </select>
                    </div>

                    <!-- Add Introduction field here -->
                    <div class="form-group full-width">
                        <label for="introduction">Introduction <span class="required">*</span></label>
                        <textarea
                            id="introduction"
                            name="introduction"
                            maxlength="300"
                            required
                            placeholder="Brief introduction about yourself and your expertise"
                        ></textarea>
                        <div id="introCounter" class="char-counter"></div>
                    </div>

                    <div class="form-group">
                        <label>Business Registration <span class="required">*</span></label>
                        <div class="id-upload-box" onclick="document.getElementById('businessReg').click()">
                            <input type="file"
                                id="businessReg"
                                name="businessReg"
                                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                                required
                                hidden>
                            <div class="id-preview" id="businessRegPreview">
                                <i class="fas fa-file-alt"></i>
                                <span>Click to Upload</span>
                            </div>
                        </div>
                        <div class="error-message" id="businessReg-error" style="display: none;"></div>
                    </div>

                    <div class="checkbox-group full-width">
                        <input type="checkbox" id="emailSubscription" name="email_updates">
                        <label for="emailSubscription">
                            I want to receive email updates about <span style="color: var(--primary-pink);">GigGenius</span> services and latest opportunities.
                        </label>
                    </div>

                    <div class="checkbox-group full-width">
                        <input type="checkbox" id="termsAgreement" name="terms_agreement" required>
                        <label for="termsAgreement">
                            I understand and accept the GigGenius <a href="{{ url_for('terms_of_service') }}">Terms of Service</a>, <a href="{{ url_for('user_agreement') }}">User Agreement,</a> and <a href="{{ url_for('privacy_policy') }}">Privacy Policy.</a>
                        </label>
                    </div>
                </div>

                <!-- Error messages container for step 2 -->
                <div id="validation-errors-step2" class="error-container" style="display: none;"></div>

                <div class="button-container">
                    <button type="button" onclick="previousStep()" class="btn btn-outline">Previous</button>
                    <button type="button" onclick="validateStep2()" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </form>
    </div>

    <script>
        // Constants
        const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes
        let stream = null;

        // Country codes mapping
        const countryCodes = {
            'Philippines': '+63',
            'United States': '+1',
            'United Kingdom': '+44',
            'Canada': '+1',
            'Australia': '+61',
            'Singapore': '+65',
            'Japan': '+81',
            'South Korea': '+82',
            'India': '+91',
            'Malaysia': '+60',
            'Indonesia': '+62',
            'Thailand': '+66',
            'Vietnam': '+84',
            'New Zealand': '+64',
            'Germany': '+49',
            'France': '+33',
            'Italy': '+39',
            'Spain': '+34',
            'Netherlands': '+31',
            'Sweden': '+46',
            'Norway': '+47',
            'Denmark': '+45',
            'Finland': '+358',
            'Ireland': '+353',
            'Switzerland': '+41',
            'Belgium': '+32',
            'Austria': '+43',
            'Portugal': '+351',
            'Greece': '+30'
        };

        // DOM Content Loaded Event
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            setMaxBirthdayDate();
            initializeCountryCode();
            setupRealTimeValidation();
            const introTextarea = document.getElementById('introduction');
            const introCounter = document.getElementById('introCounter');

            introTextarea.addEventListener('input', function() {
                const remaining = this.value.length;
                introCounter.textContent = `${remaining}/300`;

                if (remaining >= 300) {
                    introCounter.style.color = 'red';
                } else {
                    introCounter.style.color = '';
                }
            });

            // Character counter for PWD condition
            createCharCounter('pwdCondition', 500, 'pwdConditionCounter');
        });

        // Initialization Functions
        function initializeEventListeners() {
            // File upload handlers
            document.getElementById('profilePhoto').addEventListener('change', handleProfilePhotoUpload);
            document.getElementById('businessLogo').addEventListener('change', handleBusinessLogoUpload);
            document.getElementById('businessReg').addEventListener('change', handleBusinessRegUpload);

            // PWD checkbox handling
            document.getElementById('isPwd').addEventListener('change', handlePwdCheckboxChange);

            // PWD proof upload
            document.getElementById('pwdProof').addEventListener('change', handlePwdProofUpload);

            // Form validation handlers
            document.getElementById('email').addEventListener('input', validateEmailInput);
            document.getElementById('password').addEventListener('input', validatePasswordInput);
            document.getElementById('mobile').addEventListener('input', validateMobileInput);
            document.getElementById('businessEmail').addEventListener('input', validateBusinessEmailInput);
            document.getElementById('birthday').addEventListener('input', validateBirthdayInput);

            // Password toggle
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            togglePassword.addEventListener('click', function() {
                togglePasswordVisibility(passwordInput, this);
            });

            // Clear errors when user interacts
            document.querySelectorAll('input').forEach(input => {
                input.addEventListener('input', clearInputError);
            });

            document.querySelectorAll('select').forEach(select => {
                select.addEventListener('change', clearInputError);
            });

            document.getElementById('termsAgreement').addEventListener('change', clearInputError);
        }

        function setMaxBirthdayDate() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('birthday').setAttribute('max', today);
        }

        function initializeCountryCode() {
            const countrySelect = document.getElementById('country');
            const countryCodeSpan = document.getElementById('selectedCountryCode');
            countryCodeSpan.textContent = countryCodes['Philippines'];

            countrySelect.addEventListener('change', function() {
                updateCountryCode(this.value);
            });
        }

        // Add this new function
        function setupRealTimeValidation() {
            // Email real-time validation
            document.getElementById('email').addEventListener('input', function() {
                const email = this.value;
                const errorElement = document.getElementById('email-error');

                // Show error message immediately if there's no @ symbol
                if (!email.includes('@')) {
                    errorElement.style.display = 'block';
                    errorElement.textContent = 'Please enter a valid email address (e.g., <EMAIL>)';
                    this.classList.add('input-error');
                    return;
                }

                // More detailed validation if @ is present
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    errorElement.style.display = 'block';
                    errorElement.textContent = 'Please enter a valid email address (e.g., <EMAIL>)';
                    this.classList.add('input-error');
                } else {
                    errorElement.style.display = 'none';
                    this.classList.remove('input-error');
                    validateEmail(email);
                }
            });

            // Password real-time validation
            document.getElementById('password').addEventListener('input', function() {
                const password = this.value;
                const errorElement = document.getElementById('password-error');

                // If empty, hide error
                if (password.trim() === '') {
                    errorElement.style.display = 'none';
                    this.classList.remove('input-error');
                    return;
                }

                const minLength = password.length >= 8;
                const hasUpperCase = /[A-Z]/.test(password);
                const hasLowerCase = /[a-z]/.test(password);
                const hasNumber = /[0-9]/.test(password);
                const hasSpecial = /[!@#$%^&*]/.test(password);

                // Show specific error message based on what's missing
                if (!minLength || !hasUpperCase || !hasLowerCase || !hasNumber || !hasSpecial) {
                    errorElement.style.display = 'block';
                    this.classList.add('input-error');

                    let errorMessage = 'Password must:';
                    if (!minLength) errorMessage += '<br>• Be at least 8 characters';
                    if (!hasUpperCase) errorMessage += '<br>• Include an uppercase letter (A-Z)';
                    if (!hasLowerCase) errorMessage += '<br>• Include a lowercase letter (a-z)';
                    if (!hasNumber) errorMessage += '<br>• Include a number (0-9)';
                    if (!hasSpecial) errorMessage += '<br>• Include a special character (!@#$%^&*)';

                    errorElement.innerHTML = errorMessage;
                } else {
                    errorElement.style.display = 'none';
                    this.classList.remove('input-error');
                }
            });

            // Birthday real-time validation
            document.getElementById('birthday').addEventListener('input', function() {
                validateBirthdayInput.call(this); // Call with correct context
            });
        }

        // Helper Functions
        function updateCountryCode(country) {
            const countryCodeSpan = document.getElementById('selectedCountryCode');
            countryCodeSpan.textContent = countryCodes[country] || '+63';
        }

        function togglePasswordVisibility(passwordInput, toggleIcon) {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            toggleIcon.classList.toggle('fa-eye');
            toggleIcon.classList.toggle('fa-eye-slash');
        }

        function clearInputError() {
            this.classList.remove('input-error');
            const errorId = `${this.id}-error`;
            hideError(errorId);
        }

        // File Upload Handlers
        function handleProfilePhotoUpload(e) {
            const file = e.target.files[0];
            if (!file) return;

            if (!validateFileType(file, 'image/')) {
                displayValidationError('Please select an image file (JPEG, PNG)');
                e.target.value = '';
                return;
            }

            if (!validateFileSize(file)) {
                displayValidationError('Profile photo must be less than 5MB');
                e.target.value = '';
                return;
            }

            previewImage(file, 'photoPreview', 'photoPrompt');
        }

        function handleBusinessLogoUpload(e) {
            const file = e.target.files[0];
            if (!file) return;

            if (!validateFileType(file, 'image/')) {
                showError('businessLogo-error', 'Please select an image file (JPEG, PNG)');
                e.target.value = '';
                return;
            }

            if (!validateFileSize(file)) {
                showError('businessLogo-error', 'Business logo must be less than 5MB');
                e.target.value = '';
                return;
            }

            previewImage(file, 'businessLogoPreview', 'businessLogoPrompt');
            hideError('businessLogo-error');
        }

        function handleBusinessRegUpload(e) {
            const file = e.target.files[0];
            if (!file) return;

            const allowedTypes = [
                'application/pdf', 'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'image/jpeg', 'image/png', 'image/jpg'
            ];

            if (!validateFileType(file, allowedTypes)) {
                showError('businessReg-error', 'Please upload a valid document (PDF, Word, Excel, or Image file only)');
                e.target.value = '';
                return;
            }

            if (!validateFileSize(file)) {
                showError('businessReg-error', 'Business registration file must be less than 5MB');
                e.target.value = '';
                return;
            }

            updateBusinessRegPreview(file);
            hideError('businessReg-error');
        }

        function validateFileType(file, allowedTypes) {
            if (Array.isArray(allowedTypes)) {
                return allowedTypes.includes(file.type);
            }
            return file.type.startsWith(allowedTypes);
        }

        function validateFileSize(file) {
            return file.size <= MAX_FILE_SIZE;
        }

        function previewImage(file, previewId, promptId) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById(previewId);
                const prompt = document.getElementById(promptId);

                preview.src = e.target.result;
                preview.style.display = 'block';
                prompt.style.display = 'none';
            };
            reader.readAsDataURL(file);
        }

        function updateBusinessRegPreview(file) {
            const preview = document.getElementById('businessRegPreview');
            const icon = preview.querySelector('i');
            const text = preview.querySelector('span');

            icon.className = 'fas fa-check-circle';
            icon.style.color = '#28a745';
            text.textContent = file.name;
        }

        // PWD-related functions
        function handlePwdCheckboxChange() {
            const isPwdChecked = this.checked;
            const pwdDetailsSection = document.getElementById('pwdDetailsSection');

            if (isPwdChecked) {
                pwdDetailsSection.style.display = 'block';
                // Make PWD fields required when checkbox is checked
                document.getElementById('pwdCondition').setAttribute('required', 'required');
                document.getElementById('pwdProof').setAttribute('required', 'required');
            } else {
                pwdDetailsSection.style.display = 'none';
                // Remove required attribute when checkbox is unchecked
                document.getElementById('pwdCondition').removeAttribute('required');
                document.getElementById('pwdProof').removeAttribute('required');
                // Clear the values
                document.getElementById('pwdCondition').value = '';
                document.getElementById('pwdProof').value = '';
                // Clear any error messages
                document.getElementById('pwdProof-error').style.display = 'none';
            }
        }

        function handlePwdProofUpload(e) {
            const file = e.target.files[0];
            if (!file) return;

            const allowedTypes = [
                'application/pdf', 'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'image/jpeg', 'image/png', 'image/jpg'
            ];

            if (!allowedTypes.includes(file.type)) {
                showError('pwdProof-error', 'Please upload a valid document (PDF, Word, or Image file only)');
                e.target.value = '';
                return;
            }

            if (file.size > MAX_FILE_SIZE) {
                showError('pwdProof-error', 'PWD proof document must be less than 5MB');
                e.target.value = '';
                return;
            }

            updatePwdProofPreview(file);
            hideError('pwdProof-error');
        }

        function updatePwdProofPreview(file) {
            const preview = document.getElementById('pwdProofPreview');
            const icon = preview.querySelector('i');
            const span = preview.querySelector('span');

            // Update the preview to show file name
            if (icon) icon.className = 'fas fa-file-check';
            if (span) span.textContent = `Selected: ${file.name}`;
        }

        // Validation Functions
        function validateEmailInput() {
            const email = this.value;
            validateEmail(email);
        }

        function validatePasswordInput() {
            const password = this.value;
            validatePassword(password);
        }

        function validateMobileInput() {
            this.value = this.value.replace(/\D/g, '');
            validateMobile(this.value);
        }

        function validateBusinessEmailInput() {
            const email = this.value;
            validateBusinessEmail(email);
        }

        function validateBirthdayInput() {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            const selectedDate = new Date(this.value);
            selectedDate.setHours(0, 0, 0, 0);

            let errorElement = document.getElementById('birthday-error');
            if (!errorElement) {
                errorElement = createErrorElement(this, 'birthday-error');
            }

            errorElement.textContent = '';
            this.classList.remove('input-error');

            if (!this.value.trim()) {
                errorElement.style.display = 'none';
                return false;
            }

            if (selectedDate > today) {
                errorElement.textContent = 'Birthday cannot be a future date';
                errorElement.style.display = 'block';
                this.classList.add('input-error');
                return false;
            }

            const [year, month, day] = this.value.split('-').map(Number);
            const currentYear = today.getFullYear();

            if (year > currentYear) {
                errorElement.textContent = `Year cannot exceed ${currentYear}`;
                errorElement.style.display = 'block';
                this.classList.add('input-error');
                return false;
            }

            if (month < 1 || month > 12) {
                errorElement.textContent = 'Month must be between 01-12';
                errorElement.style.display = 'block';
                this.classList.add('input-error');
                return false;
            }

            const daysInMonth = new Date(year, month, 0).getDate();
            if (day < 1 || day > daysInMonth) {
                errorElement.textContent = `Day must be between 01-${daysInMonth.toString().padStart(2, '0')}`;
                errorElement.style.display = 'block';
                this.classList.add('input-error');
                return false;
            }

            errorElement.style.display = 'none';
            return true;
        }

        function createErrorElement(inputElement, id) {
            const errorElement = document.createElement('div');
            errorElement.id = id;
            errorElement.className = 'error-message';
            inputElement.parentNode.insertBefore(errorElement, inputElement.nextSibling);
            return errorElement;
        }

        async function validateEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const isValid = re.test(email);
            const errorElement = document.getElementById('email-error');

            if (!isValid) {
                showError('email-error', 'Please enter a valid email address (e.g., <EMAIL>)');
                document.getElementById('email').classList.add('input-error');
                return false;
            }

            try {
                const response = await fetch('/check_email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                });

                const data = await response.json();

                if (data.exists) {
                    showError('email-error', 'This email is already registered');
                    document.getElementById('email').classList.add('input-error');
                    return false;
                }

                hideError('email-error');
                document.getElementById('email').classList.remove('input-error');
                return true;
            } catch (error) {
                console.error('Error checking email:', error);
                return false;
            }
        }

        function validatePassword(password) {
            const minLength = password.length >= 8;
            const hasUpperCase = /[A-Z]/.test(password);
            const hasLowerCase = /[a-z]/.test(password);
            const hasNumber = /[0-9]/.test(password);
            const hasSpecial = /[!@#$%^&*]/.test(password);
            const isValid = minLength && hasUpperCase && hasLowerCase && hasNumber && hasSpecial;
            const errorElement = document.getElementById('password-error');

            if (password.trim() === '') {
                hideError('password-error');
                return false;
            }

            if (!isValid) {
                let errorMessage = 'Password must:';
                if (!minLength) errorMessage += '<br>• Be at least 8 characters';
                if (!hasUpperCase) errorMessage += '<br>• Include an uppercase letter (A-Z)';
                if (!hasLowerCase) errorMessage += '<br>• Include a lowercase letter (a-z)';
                if (!hasNumber) errorMessage += '<br>• Include a number (0-9)';
                if (!hasSpecial) errorMessage += '<br>• Include a special character (!@#$%^&*)';

                showError('password-error', errorMessage);
                return false;
            }

            hideError('password-error');
            return true;
        }

        function validateMobile(mobile) {
            const errorElement = document.getElementById('mobile-error');

            if (mobile.trim() === '') {
                hideError('mobile-error');
                return;
            }
        }

        async function validateBusinessEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const errorElement = document.getElementById('businessEmail-error');
            const businessEmailInput = document.getElementById('businessEmail');

            if (!re.test(email)) {
                showError('businessEmail-error', 'Please enter a valid email address (e.g., <EMAIL>)');
                businessEmailInput.classList.add('input-error');
                return false;
            }

            try {
                const response = await fetch('/check_email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        type: 'client'
                    })
                });

                const data = await response.json();

                if (data.exists) {
                    showError('businessEmail-error', 'This email is already registered');
                    businessEmailInput.classList.add('input-error');
                    return false;
                }

                hideError('businessEmail-error');
                businessEmailInput.classList.remove('input-error');
                return true;
            } catch (error) {
                console.error('Error checking email:', error);
                return false;
            }
        }

        // Form Step Navigation
        async function validateStep1() {
            const errors = [];
            // Don't clear all errors at the start
            // clearErrors(); - Remove this line

            // Profile photo validation
            const profilePhoto = document.getElementById('profilePhoto');
            if (!profilePhoto.files || profilePhoto.files.length === 0) {
                errors.push('Profile photo is required');
            } else if (profilePhoto.files[0].size > MAX_FILE_SIZE) {
                errors.push('Profile photo must be less than 5MB');
            }

            // Required fields validation
            const requiredFields = [
                { id: 'firstName', label: 'First Name' },
                { id: 'lastName', label: 'Last Name' },
                { id: 'email', label: 'Email' },
                { id: 'password', label: 'Password' },
                { id: 'birthday', label: 'Birthday' },
                { id: 'country', label: 'Country' },
                { id: 'mobile', label: 'Mobile Number' },
                { id: 'position', label: 'Job Title' }
            ];

            requiredFields.forEach(field => {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    errors.push(`${field.label} is required`);
                    element.classList.add('input-error');
                }
            });

            // Validate email format and uniqueness
            const email = document.getElementById('email').value;
            if (email.trim()) {
                const isEmailValid = await validateEmail(email);
                if (!isEmailValid) {
                    errors.push('Please enter a valid email address');
                }
            }

            // Validate password
            const password = document.getElementById('password').value;
            if (password.trim()) {
                const minLength = password.length >= 8;
                const hasUpperCase = /[A-Z]/.test(password);
                const hasLowerCase = /[a-z]/.test(password);
                const hasNumber = /[0-9]/.test(password);
                const hasSpecial = /[!@#$%^&*]/.test(password);

                if (!minLength || !hasUpperCase || !hasLowerCase || !hasNumber || !hasSpecial) {
                    errors.push('Password does not meet requirements');
                }
            }

            // Validate birthday
            const birthdayInput = document.getElementById('birthday');
            if (birthdayInput.value.trim()) {
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                const selectedDate = new Date(birthdayInput.value);
                selectedDate.setHours(0, 0, 0, 0);

                if (selectedDate > today) {
                    errors.push('Please enter a valid birthday');
                    birthdayInput.classList.add('input-error');
                    showError('birthday-error', 'Please enter a valid birthday');
                }
            }

            // PWD validation
            const isPwdChecked = document.getElementById('isPwd').checked;
            if (isPwdChecked) {
                const pwdCondition = document.getElementById('pwdCondition').value.trim();
                const pwdProof = document.getElementById('pwdProof');

                if (!pwdCondition) {
                    errors.push('PWD condition description is required');
                    document.getElementById('pwdCondition').classList.add('input-error');
                }

                if (!pwdProof.files || pwdProof.files.length === 0) {
                    errors.push('PWD proof document is required');
                } else if (pwdProof.files[0].size > MAX_FILE_SIZE) {
                    errors.push('PWD proof document must be less than 5MB');
                }
            }

            if (errors.length > 0) {
                displayValidationErrors(errors, 'validation-errors');
                return false;
            }

            nextStep();
            return true;
        }

        async function validateStep2() {
            let errors = [];
            clearErrors();

            // Business logo validation
            const businessLogo = document.getElementById('businessLogo');
            if (!businessLogo.files || businessLogo.files.length === 0) {
                errors.push('Business Logo is required');
                showError('businessLogo-error', 'Business Logo is required');
            } else if (businessLogo.files[0].size > MAX_FILE_SIZE) {
                errors.push('Business logo must be less than 5MB');
                showError('businessLogo-error', 'Business logo must be less than 5MB');
            }

            // Validate all required text fields
            const requiredFields = [
                { id: 'businessName', label: 'Business Name' },
                { id: 'businessAddress', label: 'Business Address' },
                { id: 'businessEmail', label: 'Business Email' }
            ];

            requiredFields.forEach(field => {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    errors.push(`${field.label} is required`);
                    element.classList.add('input-error');
                    showError(`${field.id}-error`, `${field.label} is required`);
                }
            });

            // Validate business email
            const businessEmail = document.getElementById('businessEmail').value;
            if (businessEmail.trim()) {
                const isBusinessEmailValid = await validateBusinessEmail(businessEmail);
                if (!isBusinessEmailValid) {
                    errors.push('Business email is Invalid');
                }
            }

            // Validate dropdowns
            const industry = document.getElementById('industry');
            if (!industry.value) {
                errors.push('Industry is required');
                industry.classList.add('input-error');
                showError('industry-error', 'Industry is required');
            }

            const employeeCount = document.getElementById('employeeCount');
            if (!employeeCount.value) {
                errors.push('Number of employees is required');
                employeeCount.classList.add('input-error');
                showError('employeeCount-error', 'Number of employees is required');
            }

            const introduction = document.getElementById('introduction');
            if (!introduction.value.trim()) {
                errors.push('Introduction is required');
                introduction.classList.add('input-error');
                showError('introduction-error', 'Introduction is required');
            }

            // Business registration document validation
            const businessReg = document.getElementById('businessReg');
            if (!businessReg.files || businessReg.files.length === 0) {
                errors.push('Business registration document is required');
                showError('businessReg-error', 'Business registration document is required');
            } else {
                const allowedTypes = [
                    'application/pdf', 'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'image/jpeg', 'image/png', 'image/jpg'
                ];

                if (!validateFileType(businessReg.files[0], allowedTypes)) {
                    errors.push('Business registration must be a PDF, Word, Excel, or Image file');
                    showError('businessReg-error', 'Business registration must be a PDF, Word, Excel, or Image file');
                } else if (!validateFileSize(businessReg.files[0])) {
                    errors.push('Business registration file must be less than 5MB');
                    showError('businessReg-error', 'Business registration file must be less than 5MB');
                }
            }

            // Terms agreement validation
            const termsAgreement = document.getElementById('termsAgreement');
            if (!termsAgreement.checked) {
                errors.push('You must accept the Terms and Conditions');
                termsAgreement.classList.add('input-error');
                showError('termsAgreement-error', 'You must accept the Terms and Conditions');
            }

            if (errors.length > 0) {
                displayValidationErrors(errors, 'validation-errors-step2');
                return false;
            }

            submitForm();
            return true;
        }

        function nextStep() {
            document.getElementById('section1').classList.add('hidden');
            document.getElementById('section2').classList.remove('hidden');
            document.getElementById('step1').classList.remove('active');
            document.getElementById('step2').classList.add('active');
        }

        function previousStep() {
            document.getElementById('section2').classList.add('hidden');
            document.getElementById('section1').classList.remove('hidden');
            document.getElementById('step2').classList.remove('active');
            document.getElementById('step1').classList.add('active');
        }

        // Form Submission
        async function submitForm() {
            const form = document.getElementById('registrationForm');
            const formData = new FormData(form);

            // Set checkbox values
            formData.set('email_updates', document.getElementById('emailSubscription').checked ? '1' : '0');
            formData.set('terms_agreement', document.getElementById('termsAgreement').checked ? '1' : '0');
            formData.set('is_pwd', document.getElementById('isPwd').checked ? '1' : '0');

            try {
                showLoadingOverlay();
                const response = await fetch('/register_client', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    hideLoadingOverlay();
                    document.getElementById('successModal').style.display = 'block';
                } else {
                    throw new Error(data.error || 'Registration failed');
                }
            } catch (error) {
                console.error('Error:', error);
                hideLoadingOverlay();
                displayValidationError(error.message.includes('too large') ?
                    'File upload failed: Image size must be less than 5MB' :
                    'Registration failed: ' + error.message
                );
            }
        }

        // Error Handling Functions
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            if (errorElement) {
                errorElement.innerHTML = message;
                errorElement.style.display = 'block';
            }
        }

        function hideError(elementId) {
            const errorElement = document.getElementById(elementId);
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        }

        function displayValidationErrors(errors, containerId) {
            const errorContainer = document.getElementById(containerId);
            if (errorContainer) {
                errorContainer.innerHTML = `
                    <ul>
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                `;
                errorContainer.style.display = 'block';
                errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        function displayValidationError(message) {
            const errorContainer = document.getElementById('validation-errors-step2');
            errorContainer.innerHTML = `
                <ul>
                    <li>${message}</li>
                </ul>
            `;
            errorContainer.style.display = 'block';
            errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        function clearErrors() {
            document.querySelectorAll('.input-error').forEach(el => el.classList.remove('input-error'));
            document.querySelectorAll('.error-message').forEach(el => el.style.display = 'none');
            document.querySelectorAll('.error-container').forEach(el => {
                el.style.display = 'none';
                el.innerHTML = '';
            });
        }

        // Navigation Functions
        function goBack() {
            if (document.referrer) {
                window.history.back();
            } else {
                window.location.href = "{{ url_for('landing_page') }}";
            }
        }

        // Loading Overlay Functions
        function showLoadingOverlay() {
            const overlay = document.createElement('div');
            overlay.id = 'loadingOverlay';
            overlay.className = 'loading-overlay show';
            overlay.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Processing your registration...</p>
                </div>
            `;
            document.body.appendChild(overlay);
        }

        function hideLoadingOverlay() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.remove();
            }
        }

        // Character counter function
        function createCharCounter(textareaId, maxLength, counterId) {
            const textarea = document.getElementById(textareaId);
            const counter = document.getElementById(counterId);

            if (textarea && counter) {
                textarea.addEventListener('input', function() {
                    const remaining = this.value.length;
                    counter.textContent = `${remaining}/${maxLength}`;

                    if (remaining >= maxLength) {
                        counter.style.color = 'red';
                    } else {
                        counter.style.color = '';
                    }
                });
            }
        }
    </script>
</body>
</html>