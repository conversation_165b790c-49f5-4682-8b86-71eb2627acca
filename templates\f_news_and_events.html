<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>News and Events | GigGenius</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        html {
            font-size: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        /* Prevent body scroll when modal is open - but allow on mobile */
        body.modal-open {
            overflow: hidden;
        }

        /* Hide navbar when modal is open */
        body.modal-open .navbar {
            display: none !important;
        }

        @media (max-width: 768px) {
            body.modal-open {
                overflow: auto !important;
                position: static !important;
            }
        }

        /* Button Styles */
        .auth-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
            position: relative;
        }

        .btn {
            padding: 0 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
            height: 50px;
        }

        .btn-primary {
            background: var(--primary-pink);
            border: 2px solid var(--primary-pink);
            color: var(--text-light);
        }

        .btn-outline {
            background: white;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-gigs {
            background: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            color: var(--text-light);
        }

        .btn-primary:hover {
            background: white;
            border: 2px solid var(--primary-pink);
            color: var(--primary-pink);
            text-decoration: none;
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: var(--text-light);
            text-decoration: none;
        }

        .btn-gigs:hover {
            background: white;
            color: var(--primary-blue);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            justify-content: center;
            align-items: center;
            overflow: hidden; /* Prevent scrolling */
        }

        .modal-content {
            background-color: var(--text-light);
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 450px;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            max-height: 85vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            z-index: 1001;
        }

        .modal-content h2 {
            font-size: 24px;
            color: var(--primary-blue);
        }

        .modal-buttons {
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            padding: 0 2rem;
        }

        .modal-buttons .btn {
            width: auto;
            min-width: 160px;
            max-width: 80%;
            margin: 0 auto;
        }

        .social-login-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .social-login-btn img {
            width: 20px;
            height: 20px;
            object-fit: contain;
        }

        .close {
            position: absolute;
            right: 1rem;
            top: 0.1rem;
            font-size: 2rem;
            cursor: pointer;
        }

        .role-selection {
            margin: 2rem 0;
        }

        .role-option {
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .role-option:hover {
            background-color: var(--text-light);
        }

        .role-option.selected {
            border-color: var(--primary-pink);
            background-color: var(--text-light);
        }

        .role-option input[type="radio"] {
            margin-right: 0.5rem;
        }

        .login-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .login-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .login-modal-content {
            max-width: 400px;
            padding: 2rem;
        }

        .login-container {
            width: 100%;
        }

        .login-container h2 {
            text-align: center;
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
        }

        .login-container form .btn-primary {
            width: auto;
        }

        .form-group {
            position: relative;
            margin-bottom: 1rem;
        }

        .form-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-gray);
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem 1rem 0.8rem 2.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .checkbox-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .forgot-password-link {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password-link:hover {
            text-decoration: underline;
        }

        .or-separator {
            text-align: center;
            margin: 1rem 0;
            position: relative;
        }

        .or-separator::before,
        .or-separator::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 45%;
            height: 1px;
            background-color: #ddd;
        }

        .or-separator::before {
            left: 0;
        }

        .or-separator::after {
            right: 0;
        }

        .signup-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .signup-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        .forgot-password-modal {
            max-width: 400px;
            padding: 2rem;
        }

        .forgot-password-modal h2 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .forgot-password-modal input {
            width: 100%;
            padding: 0.8rem;
            margin: 1rem 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        /* Container Styles */
        .container {
            max-width: 2000px;
            margin: auto;
            padding: auto;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0rem 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5.5rem;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.7rem;
            color: var(--primary-pink);
            margin-right: 1rem;
        }

        .logo img {
            width: 5rem;
            height: 5rem;
        }

        .logo h1 {
            font-size: 1.7rem;
            margin-left: -0.5rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .search-auth-content {
            display: flex;
            align-items: center;
            gap: 0;
            padding: 0.5rem 1rem;
        }

        .search-type-select {
            position: relative;
        }

        .search-type-button {
            height: 50px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1rem;
            border: 2px solid var(--primary-blue);
            border-radius: 8px 0 0 8px;
            border-right: none;
            background: white;
            cursor: pointer;
            font-size: 1rem;
            color: var(--primary-blue);
        }

        .search-type-button:hover {
            color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .search-type-button:after {
            content: '▼';
            font-size: 1rem;
        }

        .search-type-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            margin-top: 0.5rem;
            display: none;
            z-index: 1000;
            width: max-content;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .search-type-dropdown.active {
            display: block;
        }

        .search-type-option {
            padding: 1rem;
            cursor: pointer;
            color: var(--primary-blue);
        }

        .search-type-option:hover {
            color: var(--primary-pink);
        }

        .search-bar {
            height: 50px;
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 0 8px 8px 0;
            width: 200px;
            margin-right: 1rem;
        }

        .search-bar:hover {
            border-color: var(--primary-pink);
        }

        .search-type-button:hover + .search-bar {
            border-color: var(--primary-pink);
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar input:focus {
            outline: none;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 0.5rem;
            font-size: 1rem;
        }

        .search-bar:hover .icon {
            color: var(--primary-pink);
        }

        /* Footer Styles */
        footer {
            background: var(--primary-blue);
            padding: 2.5rem 5%;
            align-items: center;
            padding-bottom: 2rem;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
            margin-bottom: 2rem;
        }

        .footer-column h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .footer-column a {
            font-size: 1.1rem;
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
        }

        .footer-column a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            font-size: 1.1rem;
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid white;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10rem;
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        .footer-bottom .social-icons img {
            width: 1rem;
            height: 1rem;
        }

        .social-icons .bi {
            font-size: 1.5rem;
            margin-right: 10px;
            border-radius: 50%;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons .bi:hover {
            transform: scale(1.2);
            color: var(--primary-pink);
        }

        /* Hamburger Menu Styles */
        .hamburger {
            position: relative; /* Change from fixed to relative */
            display: none;
            cursor: pointer;
            padding: 15px;
            background: none;
            border: none;
            z-index: 1001;
        }

        .hamburger span {
            display: block;
            width: 25px;
            height: 3px;
            margin: 5px 0;
            background-color: var(--primary-blue);
            transition: all 0.3s ease;
        }

        /* Side nav styles */
        .side-nav {
            position: fixed;
            top: 2rem;
            left: -100%;
            height: 100vh;
            width: 100%;
            background-color: white;
            z-index: 1000;
            transition: left 0.3s ease;
        }

        .side-nav.active {
            left: 0;
        }

        .side-nav-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
            display: none;
        }

        .side-nav-overlay.active {
            display: block;
        }

        .side-nav-content {
            padding: 60px 0 20px 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            width: 100%;
            max-width: 300px;
            padding: 20px;
            color: var(--primary-blue);
            text-decoration: none;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
            font-size: 1.2rem;
            text-align: center;
            margin: 10px 0;
            font-weight: 500;
        }

        .nav-item:hover {
            color: var(--primary-pink);
        }

        .side-nav.active + .hamburger span {
            background-color: var(--primary-blue);
        }

                /* Main Content Styles */
                .main-content {
                max-width: 1600px;
                margin: 0 auto;
                padding: 1rem;
            }
    /* Intro Section Styles */
    .intro-section {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
        color: white;
        padding: 6rem 2rem;
        text-align: center;
        margin-bottom: 3rem;
        width: 100%;
        position: relative;
        overflow: hidden;
        border-radius: 15px;
    }

    .intro-section::before {
        content: '';
        position: absolute;
        top: -150%;
        left: -150%;
        width: 400%;
        height: 400%;
        background-image: url('../static/img/giggenius_logo.jpg');
        background-size: 150px 150px;
        background-repeat: repeat;
        transform: rotate(-15deg);
        opacity: 0.08;
        pointer-events: none;
        z-index: 1;
    }

    .intro-section .highlight {
        color: var(--yellow);
    }

    .intro-container {
        position: relative;
        z-index: 2;
    }

    .intro-title {
        font-size: 3.5rem;
        margin-bottom: 1rem;
    }

    .intro-text {
        color: var(--text-light);
        font-size: 1.25rem;
        line-height: 1.8;
        max-width: 900px;
        margin: 0 auto;
        margin-bottom: 2rem;
    }

    .intro-text:last-child {
        margin-bottom: 0;
    }

    .create-post-btn {
        margin-top: 1.5rem;
        padding: 0.8rem 1.5rem;
        font-size: 1.1rem;
        border: none;
    }
        /* TV */
        @media screen and (min-width: 1921px) and (max-width: 3840px) {
            .section1 .image {
                display: none;
            }
        }

        @media screen and (max-width: 1500px) {
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                z-index: 1003;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
        }

        @media screen and (max-width: 1300px) {
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
        }

        /* Tablets & Small Laptops */
        @media screen and (max-width: 1200px) {
            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
            }
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                z-index: 1003;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
        }

        @media screen and (max-width: 1024px) {
            .hamburger {
                display: block;
            }
            .nav-links {
                display: none;
            }
            .logo h1 {
                display: none;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
        }

        /* Mobile Devices */
        @media screen and (max-width: 768px) {
            .intro-section {
                padding: 4rem 1rem;
                margin-bottom: 2rem;
            }
            .intro-title {
                font-size: 2.5rem;
            }
            .intro-text {
                font-size: 1.1rem;
            }
            .filter-options {
                flex-direction: column;
                align-items: center;
                gap: 0.5rem;
                margin-bottom: 1.5rem;
            }
            .filter-options select {
                width: 100%;
                max-width: 300px;
            }
            .news-events-container {
                padding: 0 0.5rem;
            }
            .news-event-card {
                margin-bottom: 1.5rem;
            }
            .news-event-image {
                height: 200px;
            }
            .news-event-content {
                padding: 1.5rem;
            }
            .news-event-content h3 {
                font-size: 1.5rem;
            }
            .event-date {
                font-size: 1rem;
            }
            .news-content {
                font-size: 1rem;
            }
            footer {
                padding: 2rem 1rem;
            }
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin-bottom: 1.5rem;
            }
            .footer-column {
                margin-bottom: 1rem;
                border-bottom: 2px solid rgba(255, 255, 255, 0.2);
                padding-bottom: 1rem;
            }
            .footer-column h3 {
                font-size: 1.5rem;
                margin-bottom: 0.8rem;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-weight: 600;
                gap: 0.5rem;
            }
            .footer-column h3::after {
                content: '▼';
                font-size: 1rem;
                transition: transform 0.3s ease;
                margin-left: auto;
            }
            .footer-column h3.active::after {
                transform: rotate(180deg);
            }
            .footer-column .footer-links {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
            }
            .footer-column .footer-links.show {
                max-height: 500px;
            }
            .footer-column a {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
                display: block;
            }
            .footer-bottom {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
                border-top: none;
                padding: 0;
            }
            .footer-bottom p {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
            }
            .social-icons {
                display: flex;
                justify-content: center;
                margin-top: 0.5rem;
            }
            .footer-bottom .social-icons img {
                width: 1.5rem;
                height: 1.5rem;
                background: white;
                border-radius: 50%;
                padding: 0.25rem;
            }
            .social-icons a {
                margin-right: 0.2rem;
            }
        }

        @media screen and (max-width: 600px) {
            .sidebar {
                width: 100% !important;
                margin-bottom: 2rem;
            }
            .main-content {
                padding: 0 1rem !important;
            }
            .section-content {
                padding: 0 1rem;
            }
        }

        /* Small Mobile Devices */
        @media screen and (max-width: 480px) {
            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
                min-width: 320px; /* Add minimum width */
            }
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0;
                background: white;
                z-index: 1003;
            }
            .hamburger {
                display: block;
            }
            .nav-links {
                display: none;
            }
            .logo {
                margin-left: -0.5rem;
            }
            .logo h1 {
                display: none;
            }
            .logo img {
                width: 3.5rem;
                height: 3.5rem;
            }
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
            .navbar .auth-buttons {
                display: flex;
                gap: 0.4rem;
                align-items: center;
                position: relative;
            }
            .footer-column h3 {
                font-size: 1.2rem;
            }
            .footer-column a {
                font-size: 1rem;
            }
            .footer-bottom p {
                font-size: 1.1rem;
            }
            .social-icons {
                display: flex;
                justify-content: center;
                margin-top: 0.5rem;
            }
            .footer-bottom .social-icons img {
                width: 1.5rem;
                height: 1.5rem;
                background: white;
                border-radius: 50%;
                padding: 0.25rem;
            }
            .social-icons a {
                margin-right: 0.2rem;
            }
            .hamburger.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
            }

            .hamburger.active span:nth-child(2) {
                opacity: 0;
            }

            .hamburger.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -7px);
            }
        }

        /* Extra small mobile devices */
        @media screen and (max-width: 360px) {
            /* Header/Navbar adjustments */
            .navbar {
                padding: 0.3rem 0.8rem;
            }

            .navbar .auth-buttons .btn {
                padding: 0.3rem 0.6rem;
                font-size: 0.75rem;
                min-width: 55px;
                height: 32px;
            }

            .logo img {
                width: 2.5rem;
                height: 2.5rem;
            }
            .modal-content,
            .login-modal-content,
            .forgot-password-modal,
            .security-code-modal {
                width: 95%;
                max-height: 85vh;
                overflow-y: auto;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 10000;
                padding: 1.5rem;
                align-items: center;
                text-align: center;
                box-sizing: border-box;
            }
            .modal h2,
            .modal p,
            .modal .login-container,
            .modal .signup-link,
            .modal .login-link,
            .modal .forgot-password-link,
            .modal .checkbox-container,
            .modal .or-separator {
                text-align: center;
            }
            .form-group {
                width: 100%;
                max-width: 100%;
                text-align: center;
                margin-bottom: 15px;
            }
            .form-group input {
                width: 100%;
                text-align: center;
                padding: 12px;
            }
            .role-selection {
                width: 100%;
                text-align: center;
            }
            .role-option {
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 15px;
                margin: 10px 0;
            }
            .social-login-btn {
                width: 100%;
                justify-content: center;
                margin: 10px auto;
            }
            .modal-buttons {
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .modal-buttons .btn {
                min-width: 140px;
                padding: 0.8rem 1.2rem;
                margin: 0 auto;
            }
            #loginModal,
            #joinModal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.7);
                align-items: center;
                justify-content: center;
                z-index: 9999;
            }

            .section {
                margin-bottom: 2rem;
            }

            .section-content {
                font-size: 0.9rem;
            }

            .section h2 {
                font-size: 1.2rem;
            }

            ul {
                padding-left: 1.5rem;
            }

            /* News and Events Mobile Styles */
            .intro-section {
                padding: 3rem 0.5rem;
            }
            .intro-title {
                font-size: 2rem;
            }
            .intro-text {
                font-size: 1rem;
            }
            .filter-options select {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }
            .news-event-content {
                padding: 1rem;
            }
            .news-event-content h3 {
                font-size: 1.3rem;
            }
            .event-date {
                font-size: 0.9rem;
            }
            .news-content {
                font-size: 0.9rem;
            }
            .no-news {
                padding: 2rem 1rem;
                font-size: 1rem;
            }
        }
        /* Small Mobile Devices */
        @media screen and (max-width: 480px) {
            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
                min-width: 320px; /* Add minimum width */
            }
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem 1rem;
                background: white;
                z-index: 1003;
                width: 100%;
                box-sizing: border-box;
            }
            .hamburger {
                display: block;
                flex-shrink: 0;
            }
            .nav-links {
                display: none;
            }
            .logo {
                margin-left: 0;
                flex-shrink: 0;
            }
            .logo h1 {
                display: none;
            }
            .logo img {
                width: 3rem;
                height: 3rem;
            }
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
            .navbar .auth-buttons {
                display: flex;
                gap: 0.5rem;
                align-items: center;
                flex-shrink: 0;
            }
            .navbar .auth-buttons .btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
                min-width: 60px;
                height: 35px;
            }

            .hamburger.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
            }

            .hamburger.active span:nth-child(2) {
                opacity: 0;
            }

            .hamburger.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -7px);
            }

            .main-content h1 {
                font-size: 1.8rem;
            }

            /* Reset modal positioning for mobile */
            .modal {
                padding: 80px 10px 10px 10px !important;
                align-items: flex-start !important;
                justify-content: center !important;
                overflow-y: auto !important;
            }

            .modal-content,
            .login-modal-content,
            .forgot-password-modal,
            .security-code-modal {
                width: calc(100vw - 20px) !important;
                max-width: 400px !important;
                padding: 20px !important;
                max-height: calc(100vh - 100px) !important;
                overflow-y: auto !important;
                position: relative !important;
                top: 0 !important;
                left: 0 !important;
                transform: none !important;
                margin: 0 auto !important;
                box-sizing: border-box !important;
                border-radius: 12px !important;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
            }

            .form-group input {
                padding: 0.7rem 1rem 0.7rem 2.2rem;
                font-size: 0.9rem;
            }

            .modal-content h2 {
                font-size: 20px;
            }

            .close {
                right: 0.5rem;
                top: 0;
            }

            /* News and Events Extra Small Mobile */
            .intro-section {
                padding: 2.5rem 0.5rem;
            }
            .intro-title {
                font-size: 1.8rem;
            }
            .intro-text {
                font-size: 0.9rem;
            }
            .create-post-btn {
                font-size: 1rem;
                padding: 0.7rem 1.2rem;
            }
            .filter-options {
                gap: 0.3rem;
            }
            .filter-options select {
                font-size: 0.8rem;
                padding: 0.3rem 0.6rem;
            }
            .news-event-image {
                height: 150px;
            }
            .news-event-content {
                padding: 0.8rem;
            }
            .news-event-content h3 {
                font-size: 1.1rem;
            }
            .event-date {
                font-size: 0.8rem;
            }
            .news-content {
                font-size: 0.8rem;
            }
        }

            /* Post Creation Modal Styles */
    #createPostModal .modal-content {
        max-width: 600px;
        position: fixed; /* Ensure it's fixed */
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%); /* Center the modal */
        max-height: 90vh; /* Prevent modal from being too tall */
        overflow-y: auto; /* Allow scrolling within modal if content is too long */
        box-sizing: border-box;
    }

    /* Mobile specific styles for post creation modal */
    @media (max-width: 768px) {
        #createPostModal .modal {
            padding: 80px 10px 10px 10px !important;
            align-items: flex-start !important;
            justify-content: center !important;
        }

        #createPostModal .modal-content {
            width: calc(100vw - 20px) !important;
            max-width: 400px !important;
            max-height: calc(100vh - 100px) !important;
            padding: 20px !important;
            margin: 0 auto !important;
            position: relative !important;
            top: 0 !important;
            left: 0 !important;
            transform: none !important;
            overflow-y: auto !important;
            border-radius: 12px !important;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
        }

        #createPostModal .form-group {
            margin-bottom: 1rem;
        }

        #createPostModal textarea {
            min-height: 100px;
            resize: vertical;
        }


    }

    #postForm .form-group {
        margin-bottom: 15px;
        text-align: left;
    }

    #postForm label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
    }

    #postForm select, #postForm textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }

    #imagePreview {
        margin-top: 10px;
        max-width: 100%;
    }

    #imagePreview img {
        max-width: 100%;
        max-height: 200px;
        border-radius: 5px;
    }

    /* Post Display Styles */
    .posts-container {
        max-width: 800px;
        margin: 20px auto;
    }

    .post-card {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }

    .post-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #eee;
    }

    .post-author {
        display: flex;
        align-items: center;
    }

    .post-author img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
    }

    .author-info {
        display: flex;
        flex-direction: column;
    }

    .author-name-dropdown {
        position: relative;
        font-weight: 600;
        cursor: pointer;
    }

    .author-name-dropdown:hover .dropdown-content {
        display: block;
    }

    .dropdown-content {
        display: none;
        position: absolute;
        background-color: white;
        min-width: 160px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        z-index: 1;
        border-radius: 5px;
    }

    .dropdown-content a {
        color: var(--text-dark);
        padding: 10px 15px;
        text-decoration: none;
        display: block;
    }

    .dropdown-content a:hover {
        background-color: #f9f9f9;
    }

    .create-author {
        color: var(--primary-blue) !important;
        border-top: 1px solid #eee;
    }

    .post-date {
        font-size: 0.8rem;
        color: var(--text-gray);
    }

    .post-options {
        cursor: pointer;
    }

    .post-content {
        padding: 15px;
    }

    .post-title {
        margin-top: 0;
        margin-bottom: 10px;
        color: var(--primary-blue);
    }

    .post-image {
        margin: 15px 0;
    }

    .post-image img {
        width: 100%;
        border-radius: 5px;
    }

    .post-stats {
        padding: 0 15px 10px;
        display: flex;
        color: var(--text-gray);
        font-size: 0.9rem;
    }

    .post-stats span {
        margin-right: 15px;
    }

    .post-actions {
        display: flex;
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .post-actions button, .reaction-container {
        flex: 1;
        padding: 10px;
        background: none;
        border: none;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--text-gray);
        font-size: 0.9rem;
    }

    .post-actions button:hover, .reaction-button:hover {
        background-color: #f5f5f5;
    }

    .reaction-container {
        position: relative;
        display: flex;
        align-items: center;
    }

    .reaction-button {
        display: flex;
        align-items: center;
        position: relative;
    }

    .reaction-options {
        display: none;
        position: absolute;
        bottom: 40px;
        background-color: white;
        border-radius: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        padding: 5px 10px;
        z-index: 10;
    }

    .reaction-button:hover .reaction-options {
        display: flex;
    }

    .reaction {
        font-size: 1.2rem;
        margin: 0 5px;
        cursor: pointer;
        transition: transform 0.2s;
    }

    .reaction:hover {
        transform: scale(1.3);
    }

    .reaction-count {
        margin-left: 5px;
        font-size: 0.9rem;
    }

    .post-comments {
        padding: 15px;
    }

    .comment-input {
        display: flex;
        align-items: center;
    }

    .comment-input img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 10px;
    }

    .comment-input input {
        flex: 1;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 20px;
    }

    /* News and Events Specific Styles */
    .filter-options {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .filter-options select {
        padding: 0.5rem 1rem;
        border: 2px solid var(--primary-blue);
        border-radius: 8px;
        background: white;
        color: var(--primary-blue);
        font-size: 1rem;
        cursor: pointer;
    }

    .filter-options select:hover {
        border-color: var(--primary-pink);
        color: var(--primary-pink);
    }

    .news-events-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .news-event-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        overflow: hidden;
        border: 2px solid var(--primary-blue);
    }

    .news-event-image {
        width: 100%;
        height: 250px;
        overflow: hidden;
    }

    .news-event-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .news-event-content {
        padding: 2rem;
    }

    .news-event-content h3 {
        color: var(--primary-blue);
        font-size: 1.8rem;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .event-date {
        color: var(--primary-pink);
        font-weight: 500;
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }

    .news-content {
        color: var(--text-dark);
        line-height: 1.6;
        font-size: 1.1rem;
    }

    .no-news {
        text-align: center;
        padding: 3rem;
        color: var(--text-gray);
        font-size: 1.2rem;
    }
    </style>
</head>
    <!-- Modal for Login -->
    <div id="loginModal" class="modal">
        <div class="modal-content login-modal-content">
            <span class="close" onclick="closeLoginModal()">&times;</span>
            <div class="login-container">
                <h2>Login to GigGenius</h2>
                <div id="loginErrorMessage" style="color: red; margin-bottom: 1rem; display: none;"></div>
                <form id="loginForm" method="POST" action="{{ url_for('login') }}">
                    <div class="form-group">
                        <i class="bi bi-envelope"></i>
                        <input type="email" name="email" id="email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <i class="bi bi-lock"></i>
                        <input type="password" name="password" id="password" placeholder="Password" required>
                    </div>
                    <div class="checkbox-container">
                        <label>
                            <input type="checkbox" id="showPassword" onclick="togglePasswordVisibility()"> Show Password
                        </label>
                        <a href="javascript:void(0)" class="forgot-password-link" onclick="openForgotPasswordModal()">Forgot Password?</a>
                    </div>
                    <button type="submit" class="btn btn-primary">LOGIN</button>
                </form>
                <div class="or-separator">or</div>
                <a href="javascript:void(0)" class="btn btn-outline social-login-btn" onclick="signInWithGoogle()">
                    <img src="{{ url_for('static', filename='img/lp2.png') }}" alt="Google">
                    <span>Continue with Google</span>
                </a>
                <a href="javascript:void(0)" class="btn btn-outline social-login-btn" onclick="signInWithLinkedIn()">
                    <img src="{{ url_for('static', filename='img/lp5.png') }}" alt="LinkedIn">
                    <span>Continue with LinkedIn</span>
                </a>
                <div class="signup-link">
                    Don't have a GigGenius account? <a href="javascript:void(0)" onclick="closeLoginModal(); openModal();">Sign Up</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Forgot Password -->
    <div id="forgotPasswordModal" class="modal">
        <div class="modal-content forgot-password-modal">
            <span class="close" onclick="closeForgotPasswordModal()">&times;</span>
            <h2>Forgot Password</h2>
            <p>Please enter your email address to reset your password.</p>
            <input type="email" id="forgotPasswordEmail" placeholder="Email" required>
            <a href="javascript:void(0)" class="btn btn-primary" onclick="submitForgotPassword()" style="width: auto;">Submit</a>
        </div>
    </div>

    <!-- Modal for Join -->
    <div id="joinModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Join as Genius or Client</h2>
            <div id="roleMessage" style="color: red; display: none;">Please select a role!</div>
            <div class="role-selection">
                <div class="role-option" onclick="selectOption('genius')">
                    <input type="radio" name="role" id="geniusRole">
                    <label for="geniusRole">
                        <i class="bi bi-person"></i> I'm a Genius (Freelancer)
                    </label>
                </div>
                <div class="role-option" onclick="selectOption('client')">
                    <input type="radio" name="role" id="clientRole">
                    <label for="clientRole">
                        <i class="bi bi-briefcase"></i> I'm a Client (Business Owner)
                    </label>
                </div>
            </div>
            <div class="modal-buttons">
                <a href="javascript:void(0)" class="btn btn-primary" onclick="continueToRegistration()">Continue</a>
            </div>
            <p class="login-link">
                Already have an account? <a href="javascript:void(0)" onclick="closeModal(); openLoginModal()">Log In</a>
            </p>
        </div>
    </div>

    <!-- Post Creation Modal -->
    <div id="createPostModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeCreatePostModal()">&times;</span>
            <h2>Create New Post</h2>
            <form id="postForm">
                <div class="form-group">
                    <input type="text" id="postTitle" placeholder="Title" required>
                </div>

                <div class="form-group">
                    <label>Category:</label>
                    <select id="postCategory" required>
                        <option value="general">General Discussion</option>
                        <option value="qa">Question and Answer</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Audience:</label>
                    <select id="postAudience" required>
                        <option value="all">For All</option>
                        <option value="geniuses">For Geniuses</option>
                        <option value="clients">For Clients</option>
                    </select>
                </div>

                <div class="form-group">
                    <textarea id="postContent" placeholder="Write your post here..." rows="5" required></textarea>
                </div>

                <div class="form-group">
                    <label for="postImage">Add Image:</label>
                    <input type="file" id="postImage" accept="image/*">
                    <div id="imagePreview"></div>
                </div>

                <button type="submit" class="btn btn-primary">Post</button>
            </form>
        </div>
    </div>
        <!-- Add this side navigation panel right after the opening <body> tag -->
        <div class="side-nav" id="sideNav">
            <div class="side-nav-content">
                <div class="nav-items">
                    <a href="{{ url_for('landing_page') }}" class="nav-item">Home</a>
                    <a href="{{ url_for('find_geniuses') }}" class="nav-item">Find Geniuses</a>
                    <a href="{{ url_for('find_gigs') }}" class="nav-item">Find Gigs</a>
                    <a href="{{ url_for('about_us') }}" class="nav-item">About Us</a>
                </div>
            </div>
        </div>
        <div class="side-nav-overlay" id="sideNavOverlay" onclick="toggleMenu()"></div>

        <!-- Main Content -->
        <div class="container">
            <!-- Navbar -->
            <nav class="navbar">
                <div style="display: flex; align-items: center;">
                    <button class="hamburger" onclick="toggleMenu()">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                    <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                        <div class="logo">
                            <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                            <h1>GigGenius</h1>
                        </div>
                    </a>
                    <div class="nav-links">
                        <a href="{{ url_for('landing_page') }}">Home</a>
                        <a href="{{ url_for('find_geniuses') }}">Find Geniuses</a>
                        <a href="{{ url_for('find_gigs') }}">Find Gigs</a>
                        <a href="{{ url_for('about_us') }}">About Us</a>
                    </div>
                </div>
                <div class="navbar-right">
                    <div class="search-auth-content">
                        <div class="search-type-select">
                            <button class="search-type-button" id="searchTypeBtn">
                                <span id="selectedSearchType">All</span>
                            </button>
                            <div class="search-type-dropdown" id="searchTypeDropdown">
                                <div class="search-type-option" data-value="all">All</div>
                                <div class="search-type-option" data-value="genius">Geniuses</div>
                                <div class="search-type-option" data-value="gigs">Gigs</div>
                            </div>
                        </div>
                        <div class="search-bar">
                            <input type="text" id="searchInput" placeholder="Search...">
                            <i class="fas fa-search icon"></i>
                        </div>
                        <div class="auth-buttons">
                            <a href="javascript:void(0)" onclick="openLoginModal()" class="btn btn-outline">Log In</a>
                            <a href="javascript:void(0)" onclick="openModal()" class="btn btn-primary">Join</a>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="main-content">
                <!-- Intro Section -->
                <section class="intro-section">
                    <div class="intro-container">
                        <h1 class="intro-title">News and Events</h1>
                        <p class="intro-text">
                            Stay updated with the latest happenings at GigGenius. From platform updates to success stories,
                            find all the information you need to stay connected with our growing community.
                        </p>
                        <p class="intro-text">
                            Have something to share? Join the conversation by creating your own post and engaging with others.
                        </p>
                        <button class="btn btn-primary create-post-btn" onclick="openCreatePostModal()">Create New Post</button>
                    </div>
                </section>

                <!-- Filter Options -->
                <div class="filter-options">
                    <select id="categoryFilter">
                        <option value="all">All Categories</option>
                        <option value="general">General Discussion</option>
                        <option value="qa">Question and Answer</option>
                    </select>

                    <select id="audienceFilter">
                        <option value="all">All Audiences</option>
                        <option value="geniuses">For Geniuses</option>
                        <option value="clients">For Clients</option>
                    </select>
                </div>

                <div class="news-events-container">
                    {% if news_events %}
                        {% for item in news_events %}
                        <div class="news-event-card">
                            {% if item.image_path %}
                            <div class="news-event-image">
                                <img src="{{ url_for('static', filename=item.image_path.replace('static/', '')) }}" alt="{{ item.title }}">
                            </div>
                            {% endif %}
                            <div class="news-event-content">
                                <h3>{{ item.title }}</h3>
                                {% if item.event_date %}
                                <div class="event-date">{{ item.event_date.strftime('%B %d, %Y') }}</div>
                                {% endif %}
                                <div class="news-content">{{ item.content }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="no-news">
                            <p>No news or events available at this time.</p>
                        </div>
                    {% endif %}
                </div>
            </main>

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                    <a href="{{ url_for('accounting_services') }}">Accounting Services</a>
                    <a href="{{ url_for('events') }}">Events & Webinars</a>
                    <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                    <a href="{{ url_for('why_cant_apply') }}">About Rejected Accounts</a>
                    <a href="{{ url_for('find_mentors') }}">GigGenius University</a>
                    <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                    <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                    <a href="{{ url_for('news_and_events') }}">News & Events</a>
                    <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('about_us') }}">About Us</a>
                    <a href="{{ url_for('contact_us') }}">Contact Us</a>
                    <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io" class="bi bi-facebook"></a>
                        <a href="https://www.instagram.com/gig.genius.io/" class="bi bi-instagram"></a>
                        <a href="https://www.threads.com/@gig.genius.io" class="bi bi-threads"></a>
                        <a href="https://twitter.com/giggenius_io" class="bi bi-twitter-x"></a>
                        <a href="https://www.tiktok.com/@giggenius.io" class="bi bi-tiktok"></a>
                        <a href="https://www.youtube.com/@giggenius" class="bi bi-youtube"></a>
                        <a href="https://www.linkedin.com/company/gig-genius/" class="bi bi-linkedin"></a>
                    </span>
                </p>
                <p>©2025 GigGenius by<a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="{{ url_for('terms_of_service') }}">Terms of Service</a> |
                    <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
                </p>
            </div>
        </footer>
    </div>

        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-33SRJGWX6H"></script>
        <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-33SRJGWX6H');
        </script>

    <script>
        // Navbar
        function toggleMenu() {
            const sideNav = document.getElementById('sideNav');
            const overlay = document.getElementById('sideNavOverlay');
            const hamburger = document.querySelector('.hamburger');
            const spans = hamburger.getElementsByTagName('span');

            sideNav.classList.toggle('active');
            overlay.classList.toggle('active');

            // Animate hamburger
            if (sideNav.classList.contains('active')) {
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -7px)';
                document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open
            } else {
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
                document.body.style.overflow = 'auto'; // Restore scrolling when menu is closed
            }
        }

        // Close side nav when clicking outside
        document.addEventListener('click', (e) => {
            const sideNav = document.getElementById('sideNav');
            const hamburger = document.querySelector('.hamburger');

            if (sideNav.classList.contains('active') &&
                !sideNav.contains(e.target) &&
                !hamburger.contains(e.target)) {
                toggleMenu();
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            const footerHeadings = document.querySelectorAll('.footer-column h3');

            footerHeadings.forEach(heading => {
                heading.addEventListener('click', function() {
                    // Toggle active class on heading
                    this.classList.toggle('active');

                    // Get the next sibling element (the links container)
                    const linksContainer = this.nextElementSibling;

                    // Toggle the show class
                    linksContainer.classList.toggle('show');
                });
            });
        });

        let selectedRole = null;

        // Modal
        function closeAllModals() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
            document.body.classList.remove('modal-open');
            document.body.style.overflow = 'auto';
        }
        function openModal() {
            closeAllModals();
            document.getElementById('joinModal').style.display = 'flex';
            document.body.classList.add('modal-open');
            document.body.style.overflow = 'hidden';
        }
        function closeModal() {
            document.getElementById('joinModal').style.display = 'none';
            document.body.classList.remove('modal-open');
            document.body.style.overflow = 'auto';
            document.getElementById('roleMessage').style.display = 'none';
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('geniusRole').checked = false;
            document.getElementById('clientRole').checked = false;
        }

        // Join Modal
        function selectOption(role) {
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });

            event.currentTarget.classList.add('selected');
            document.getElementById('roleMessage').style.display = 'none';

            if (role === 'genius') {
                document.getElementById('geniusRole').checked = true;
                document.getElementById('clientRole').checked = false;
            } else {
                document.getElementById('clientRole').checked = true;
                document.getElementById('geniusRole').checked = false;
            }
        }

        function continueToRegistration() {
            const geniusRole = document.getElementById('geniusRole').checked;
            const clientRole = document.getElementById('clientRole').checked;
            const roleMessage = document.getElementById('roleMessage');

            if (!geniusRole && !clientRole) {
                roleMessage.style.display = 'block';
                return;
            }

            if (geniusRole) {
                window.location.href = "{{ url_for('genius_registration') }}";
            } else {
                window.location.href = "{{ url_for('client_registration') }}";
            }
        }

        // Login Modal
        function openLoginModal() {
            closeAllModals();
            document.getElementById('loginModal').style.display = 'flex';
            document.body.classList.add('modal-open');
            document.body.style.overflow = 'hidden';
        }
        function closeLoginModal() {
            document.getElementById('loginModal').style.display = 'none';
            document.body.classList.remove('modal-open');
            document.body.style.overflow = 'auto';
        }
        function openForgotPasswordModal() {
            closeAllModals();
            document.getElementById('forgotPasswordModal').style.display = 'flex';
            document.body.classList.add('modal-open');
            document.body.style.overflow = 'hidden';
        }
        function closeForgotPasswordModal() {
            document.getElementById('forgotPasswordModal').style.display = 'none';
            document.body.classList.remove('modal-open');
            document.body.style.overflow = 'auto';
        }
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
        }
        function submitForgotPassword() {
            const email = document.getElementById('forgotPasswordEmail').value;
            closeForgotPasswordModal();
        }

        // Security Code Modal
        window.onclick = function(event) {
            const joinModal = document.getElementById('joinModal');
            const loginModal = document.getElementById('loginModal');
            const forgotPasswordModal = document.getElementById('forgotPasswordModal');
            const createPostModal = document.getElementById('createPostModal');

            if (event.target === joinModal) {
                closeModal();
            }
            if (event.target === loginModal) {
                closeLoginModal();
            }
            if (event.target === forgotPasswordModal) {
                closeForgotPasswordModal();
            }
            if (event.target === createPostModal) {
                closeCreatePostModal();
            }
        }

        // Scroll to Section
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Search Type Dropdown
        document.addEventListener('DOMContentLoaded', function() {
        const searchTypeBtn = document.getElementById('searchTypeBtn');
        const searchTypeDropdown = document.getElementById('searchTypeDropdown');
        const selectedSearchType = document.getElementById('selectedSearchType');
        const searchInput = document.getElementById('searchInput');
        const options = document.querySelectorAll('.search-type-option');
        // Toggle dropdown
        searchTypeBtn.addEventListener('click', function() {
            searchTypeDropdown.classList.toggle('active');
        });
        // Handle option selection
        options.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                selectedSearchType.textContent = this.textContent;
                searchTypeDropdown.classList.remove('active');
                // Update placeholder based on selection
                const placeholders = {
                    genius: 'Search for genius...',
                    gigs: 'Search for gigs...',
                    projects: 'Search for projects...',
                    all: 'Search...'
                };
                searchInput.placeholder = placeholders[value] || placeholders.all;
            });
        });
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.search-type-select')) {
                searchTypeDropdown.classList.remove('active');
            }
        });
        });

        // Login Form
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('/login', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('Login response:', data);  // Debug log

                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    const errorMessage = document.getElementById('loginErrorMessage');
                    errorMessage.textContent = data.error;
                    errorMessage.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Login error:', error);  // Debug log
                const errorMessage = document.getElementById('loginErrorMessage');
                errorMessage.textContent = 'An error occurred during login';
                errorMessage.style.display = 'block';
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Get all sidebar links
            const sidebarLinks = document.querySelectorAll('.sidebar-nav a');

            // Add click event listener to each link
            sidebarLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    sidebarLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Scroll to the target section
                    const targetId = this.getAttribute('href');
                    const targetSection = document.querySelector(targetId);
                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });

            // Open and close post creation modal
    function openCreatePostModal() {
        closeAllModals();
        document.getElementById('createPostModal').style.display = 'flex';
        document.body.classList.add('modal-open');
        document.body.style.overflow = 'hidden';
    }

    function closeCreatePostModal() {
        document.getElementById('createPostModal').style.display = 'none';
        document.body.classList.remove('modal-open');
        document.body.style.overflow = 'auto';
        document.getElementById('postForm').reset();
        document.getElementById('imagePreview').innerHTML = '';
    }

    // Image preview functionality
    document.getElementById('postImage').addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.createElement('img');
                img.src = e.target.result;
                const preview = document.getElementById('imagePreview');
                preview.innerHTML = '';
                preview.appendChild(img);
            }
            reader.readAsDataURL(file);
        }
    });

    // Handle post form submission
    document.getElementById('postForm').addEventListener('submit', function(e) {
        e.preventDefault();

        // Create FormData object
        const formData = new FormData();

        // Add form fields to FormData
        formData.append('title', document.getElementById('postTitle').value);
        formData.append('category', document.getElementById('postCategory').value);
        formData.append('audience', document.getElementById('postAudience').value);
        formData.append('content', document.getElementById('postContent').value);

        // Add image if selected
        const imageFile = document.getElementById('postImage').files[0];
        if (imageFile) {
            formData.append('image', imageFile);
        }

        console.log('Submitting post with title:', document.getElementById('postTitle').value);

        // Send data to server
        fetch('/create_post', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            // Check if response is ok before trying to parse JSON
            if (!response.ok) {
                return response.text().then(text => {
                    console.error('Server returned error response:', text);
                    throw new Error('Server error: ' + response.status);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('Server response:', data);
            if (data.success) {
                // Show success message
                alert('Post created successfully!');
                // Reload the page to show the new post
                window.location.reload();
            } else {
                // Show error message
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            alert('An error occurred while creating your post: ' + error.message);
        })
        .finally(() => {
            // Close the modal
            closeCreatePostModal();
        });
    });

    // Connect "Create New Post" button to open the modal
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listener to the Create New Post button if it exists
        const createPostBtn = document.querySelector('.create-post-btn');
        if (createPostBtn) {
            createPostBtn.addEventListener('click', openCreatePostModal);
        }
    });
    </script>
</body>
</html>
