<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Video Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-section {
            border: 2px dashed #ddd;
            padding: 40px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .upload-section.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        
        input[type="file"] {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .video-preview {
            margin: 20px 0;
            text-align: center;
        }
        
        video {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            display: none;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .video-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
            display: none;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Simple Video Upload Test</h1>
        
        <div class="upload-section" id="uploadSection">
            <h3>📁 Select or Drop Video File</h3>
            <p>Supported formats: MP4, AVI, MOV, WMV, WebM (Max: 50MB)</p>
            <input type="file" id="videoInput" accept="video/*">
            <br>
            <button onclick="selectFile()">Choose Video File</button>
        </div>
        
        <div class="status" id="statusMessage"></div>
        
        <div class="progress-bar" id="progressBar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        
        <div class="video-info" id="videoInfo"></div>
        
        <div class="video-preview" id="videoPreview" style="display: none;">
            <h3>📹 Video Preview</h3>
            <video id="previewVideo" controls></video>
            <br>
            <button onclick="uploadVideo()" id="uploadBtn" disabled>Upload to Database</button>
            <button onclick="clearVideo()">Clear</button>
        </div>
        
        <div class="video-preview" id="savedVideo" style="display: none;">
            <h3>✅ Saved Video</h3>
            <video id="savedVideoPlayer" controls></video>
            <br>
            <button onclick="deleteVideo()">Delete Video</button>
            <button onclick="replaceVideo()">Replace Video</button>
        </div>
    </div>

    <script>
        let selectedFile = null;
        let videoId = null;

        // File input change handler
        document.getElementById('videoInput').addEventListener('change', function(e) {
            handleFileSelect(e.target.files[0]);
        });

        // Drag and drop handlers
        const uploadSection = document.getElementById('uploadSection');
        
        uploadSection.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });
        
        uploadSection.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
        });
        
        uploadSection.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        function selectFile() {
            document.getElementById('videoInput').click();
        }

        function handleFileSelect(file) {
            if (!file) return;
            
            // Validate file type
            const validTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'];
            if (!validTypes.includes(file.type)) {
                showStatus('Please select a valid video file (MP4, AVI, MOV, WMV, WebM)', 'error');
                return;
            }
            
            // Validate file size (50MB max)
            const maxSize = 50 * 1024 * 1024; // 50MB
            if (file.size > maxSize) {
                showStatus('File size must be less than 50MB', 'error');
                return;
            }
            
            selectedFile = file;
            
            // Show video info
            showVideoInfo(file);
            
            // Create preview
            const previewVideo = document.getElementById('previewVideo');
            const url = URL.createObjectURL(file);
            previewVideo.src = url;
            
            // Show preview section
            document.getElementById('videoPreview').style.display = 'block';
            document.getElementById('uploadBtn').disabled = false;
            document.getElementById('savedVideo').style.display = 'none';
            
            showStatus('Video selected successfully! Ready to upload.', 'success');
        }

        function showVideoInfo(file) {
            const videoInfo = document.getElementById('videoInfo');
            videoInfo.innerHTML = `
                <strong>📄 File Information:</strong><br>
                Name: ${file.name}<br>
                Size: ${(file.size / (1024 * 1024)).toFixed(2)} MB<br>
                Type: ${file.type}<br>
                Last Modified: ${new Date(file.lastModified).toLocaleString()}
            `;
            videoInfo.style.display = 'block';
        }

        async function uploadVideo() {
            if (!selectedFile) {
                showStatus('No video selected', 'error');
                return;
            }
            
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = true;
            uploadBtn.textContent = 'Uploading...';
            
            showStatus('Uploading video to database...', 'info');
            showProgress(0);
            
            try {
                const formData = new FormData();
                formData.append('video', selectedFile);
                
                // Simulate upload progress
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += 10;
                    showProgress(progress);
                    if (progress >= 90) {
                        clearInterval(progressInterval);
                    }
                }, 200);
                
                const response = await fetch('/upload_test_video', {
                    method: 'POST',
                    body: formData
                });
                
                clearInterval(progressInterval);
                showProgress(100);
                
                const data = await response.json();
                
                if (data.success) {
                    videoId = data.video_id;
                    showStatus('Video uploaded successfully!', 'success');
                    
                    // Show saved video
                    setTimeout(() => {
                        loadSavedVideo(videoId);
                    }, 1000);
                } else {
                    showStatus('Upload failed: ' + data.error, 'error');
                }
                
            } catch (error) {
                showStatus('Upload error: ' + error.message, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = 'Upload to Database';
                hideProgress();
            }
        }

        function loadSavedVideo(id) {
            const savedVideoPlayer = document.getElementById('savedVideoPlayer');
            savedVideoPlayer.src = `/get_test_video/${id}?t=${new Date().getTime()}`;
            
            document.getElementById('videoPreview').style.display = 'none';
            document.getElementById('savedVideo').style.display = 'block';
            
            showStatus('Video loaded from database!', 'success');
        }

        function clearVideo() {
            selectedFile = null;
            videoId = null;
            document.getElementById('videoInput').value = '';
            document.getElementById('videoPreview').style.display = 'none';
            document.getElementById('savedVideo').style.display = 'none';
            document.getElementById('videoInfo').style.display = 'none';
            hideStatus();
        }

        function replaceVideo() {
            clearVideo();
            selectFile();
        }

        async function deleteVideo() {
            if (!videoId) return;
            
            if (!confirm('Are you sure you want to delete this video?')) return;
            
            try {
                const response = await fetch(`/delete_test_video/${videoId}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('Video deleted successfully!', 'success');
                    clearVideo();
                } else {
                    showStatus('Delete failed: ' + data.error, 'error');
                }
            } catch (error) {
                showStatus('Delete error: ' + error.message, 'error');
            }
        }

        function showStatus(message, type) {
            const status = document.getElementById('statusMessage');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        function hideStatus() {
            document.getElementById('statusMessage').style.display = 'none';
        }

        function showProgress(percent) {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            progressBar.style.display = 'block';
            progressFill.style.width = percent + '%';
        }

        function hideProgress() {
            document.getElementById('progressBar').style.display = 'none';
        }

        // Check for existing video on page load
        window.addEventListener('load', async function() {
            try {
                const response = await fetch('/get_latest_test_video');
                if (response.ok) {
                    const data = await response.json();
                    if (data.video_id) {
                        videoId = data.video_id;
                        loadSavedVideo(videoId);
                    }
                }
            } catch (error) {
                console.log('No existing video found');
            }
        });
    </script>
</body>
</html>
