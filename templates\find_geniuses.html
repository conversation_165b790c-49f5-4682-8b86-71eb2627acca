<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find Geniuses</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
    :root {
        --primary-blue: #004AAD;
        --primary-pink: #CD208B;
        --yellow: #FFD700;
        --text-dark: #000000;
        --text-light: #FFFFFF;
        --text-gray: #666;
        --background-light: #f8f9fa;
        --transition-speed: 0.3s;
    }
    html {
        font-size: 16px;
    }
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        transition: all 0.3s ease-in-out;
        overflow-wrap: break-word;
        word-wrap: break-word;
        word-break: break-word;
        hyphens: auto;
    }
    body {
        font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        max-width: 100%;
        overflow-x: hidden;
    }
    
    body.no-scroll {
        overflow: hidden;
    }
    /* Header Styles */
    .header {
        background-color: white;
        color: var(--text-dark);
        position: fixed;
        width: 100%;
        top: 0;
        left: 0;
        z-index: 1000;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all var(--transition-speed) ease;
    }

    .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0rem 1rem;
        max-width: 1400px;
        margin: 0 auto;
        height: 5.5rem;
    }

    .logo {
        display: flex;
        align-items: center;
        font-size: 1.5rem;
        color: var(--primary-pink);
        margin-right: 1rem;
        text-decoration: none;
    }

    .logo img {
        width: 2.5rem;
        height: 2.5rem;
        margin-right: 0.5rem;
        border-radius: 50%;
        object-fit: cover;
    }

    .logo h1 {
        font-size: 1.5rem;
        margin: 0;
        font-weight: bold;
        color: var(--primary-pink);
    }

    .nav-links {
        display: flex;
        gap: 1rem;
        list-style: none;
        align-items: center;
    }

    .nav-links a {
        color: var(--primary-blue);
        text-decoration: none;
        padding: 0.5rem 1rem;
        font-size: 1.1rem;
        font-weight: 500;
        transition: color var(--transition-speed) ease;
    }

    .nav-links a:hover, .nav-links a.active {
        color: var(--primary-pink);
    }

    .nav-buttons {
        display: flex;
        gap: 1rem;
    }

    .btn {
        padding: 0.6rem 1.5rem;
        border-radius: 30px;
        font-weight: 600;
        cursor: pointer;
        transition: all var(--transition-speed) ease;
        border: none;
        font-size: 1rem;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .btn-login {
        background-color: transparent;
        color: var(--primary-blue);
        border: 2px solid var(--primary-blue);
    }

    .btn-login:hover {
        background-color: var(--primary-blue);
        color: var(--text-light);
    }

    .btn-signup {
        background-color: var(--primary-pink);
        color: var(--text-light);
        border: 2px solid var(--primary-pink);
    }

    .btn-signup:hover {
        background-color: #a91a6d;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(205, 32, 139, 0.3);
    }

    .hamburger {
        display: none;
        cursor: pointer;
        background: none;
        border: none;
        color: var(--primary-blue);
        font-size: 1.8rem;
        padding: 0.5rem;
        min-width: 44px;
        min-height: 44px;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: background-color var(--transition-speed) ease;
    }

    .hamburger:hover {
        background-color: rgba(0, 74, 173, 0.1);
    }

    .hamburger:active {
        background-color: rgba(0, 74, 173, 0.2);
    }

    /* Mobile Menu */
    .mobile-menu {
        position: fixed;
        top: 5.5rem;
        right: -100%;
        width: 80%;
        max-width: 400px;
        height: calc(100vh - 5.5rem);
        background-color: white;
        border-left: 1px solid #eee;
        transition: right var(--transition-speed) ease;
        z-index: 999;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .mobile-menu.active {
        right: 0;
    }

    .mobile-nav-links {
        list-style: none;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .mobile-nav-links a {
        color: var(--primary-blue);
        text-decoration: none;
        font-size: 1.3rem;
        font-weight: 500;
        transition: color var(--transition-speed) ease;
        display: block;
        padding: 0.5rem 0;
        text-align: center;
    }

    .mobile-nav-links a:hover {
        color: var(--primary-pink);
    }

    .mobile-nav-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
        align-items: center;
    }

    .mobile-nav-buttons .btn {
        width: 80%;
        max-width: 250px;
        text-align: center;
    }

    .overlay {
        position: fixed;
        top: 5.5rem;
        left: 0;
        width: 100%;
        height: calc(100vh - 5.5rem);
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 998;
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-speed) ease;
    }

    .overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Responsive Styles */

    /* Large screens */
    @media (min-width: 1400px) {
        .navbar {
            padding: 0rem 2rem;
        }
    }

    /* Tablet landscape */
    @media (max-width: 1024px) {
        .nav-links {
            gap: 1.2rem;
        }

        .nav-links a {
            font-size: 1rem;
            padding: 0.4rem 0.8rem;
        }
    }

    /* Tablet portrait */
    @media (max-width: 992px) {
        .nav-links {
            gap: 1rem;
        }

        .logo img {
            width: 2.2rem;
            height: 2.2rem;
        }

        .logo h1 {
            font-size: 1.4rem;
        }

        .btn {
            min-width: 100px;
            height: 45px;
            font-size: 0.9rem;
        }
    }

    @media (max-width: 768px) {
        .nav-links, .nav-buttons {
            display: none;
        }

        .hamburger {
            display: flex;
        }

        .navbar {
            padding: 1rem 5%;
            height: 4.5rem;
        }

        .logo img {
            width: 2rem;
            height: 2rem;
        }

        .logo h1 {
            font-size: 1.3rem;
        }

        .mobile-menu {
            top: 4.5rem;
            height: calc(100vh - 4.5rem);
        }

        .overlay {
            top: 4.5rem;
            height: calc(100vh - 4.5rem);
        }

        .hero-section {
            margin-top: 4.5rem;
        }
    }

    /* Small mobile phones */
    @media (max-width: 480px) {
        .navbar {
            padding: 0.8rem 4%;
            height: 4rem;
        }

        .logo img {
            width: 1.8rem;
            height: 1.8rem;
        }

        .logo h1 {
            font-size: 1.1rem;
        }

        .mobile-menu {
            top: 4rem;
            height: calc(100vh - 4rem);
            width: 100%;
            max-width: none;
            padding: 1.5rem;
        }

        .overlay {
            top: 4rem;
            height: calc(100vh - 4rem);
        }

        .mobile-nav-links a {
            font-size: 1.2rem;
            padding: 0.6rem 0;
        }

        .mobile-nav-buttons .btn {
            height: 45px;
            font-size: 1rem;
        }

        .hero-section {
            margin-top: 4rem;
        }
    }

    /* Very small phones */
    @media (max-width: 360px) {
        .navbar {
            padding: 0.6rem 3%;
            height: 3.5rem;
        }

        .logo img {
            width: 1.5rem;
            height: 1.5rem;
        }

        .logo h1 {
            font-size: 1rem;
        }

        .mobile-menu {
            top: 3.5rem;
            height: calc(100vh - 3.5rem);
            padding: 1rem;
        }

        .overlay {
            top: 3.5rem;
            height: calc(100vh - 3.5rem);
        }

        .mobile-nav-links a {
            font-size: 1.1rem;
        }

        .mobile-nav-buttons .btn {
            width: 90%;
            height: 42px;
            font-size: 0.95rem;
        }

        .hero-section {
            margin-top: 3.5rem;
        }
    }
    .search-container {
        display: flex;
        height: 40px;
        margin-right: 1rem;
    }
    .search-type-select {
        position: relative;
        height: 100%;
    }
    .search-type-button {
        height: 50px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0 1rem;
        border: 2px solid var(--primary-blue);
        border-radius: 8px 0 0 8px;
        border-right: none;
        background: white;
        cursor: pointer;
        font-size: 1rem;
        color: var(--primary-blue);
    }
    .search-type-button:hover {
        color: var(--primary-pink);
        border-color: var(--primary-pink);
    }
    .search-type-button:after {
        content: '▼';
        font-size: 1rem;
    }
    .search-type-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        background: white;
        border: 2px solid var(--primary-blue);
        border-radius: 8px;
        margin-top: 0.5rem;
        display: none;
        z-index: 1000;
        width: max-content;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .search-type-dropdown.active {
        display: block;
    }
    .search-type-option {
        padding: 1rem;
        cursor: pointer;
        color: var(--primary-blue);
    }
    .search-type-option:hover {
        color: var(--primary-pink);
    }
    .search-bar {
        height: 50px;
        display: flex;
        align-items: center;
        background: white;
        border: 2px solid var(--primary-blue);
        border-radius: 0 8px 8px 0;
        width: 200px;
        margin-right: 1rem;
    }
    .search-bar:hover {
        border-color: var(--primary-pink);
    }
    .search-type-button:hover + .search-bar {
        border-color: var(--primary-pink);
    }
    .search-bar input {
        border: none;
        outline: none;
        padding: 0 1rem;
        width: 100%;
        height: 100%;
        font-size: 1rem;
    }
    .search-bar .icon {
        color: var(--primary-blue);
        padding: 0 0.5rem;
        font-size: 1rem;
    }
    .search-bar:hover .icon {
        color: var(--primary-pink);
    }
    .auth-buttons {
        display: flex;
        gap: 1rem;
        align-items: center;
        position: relative;
    }
    .btn {
        padding: 0 1.5rem;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 120px;
        height: 50px;
    }
    .navbar .btn {
        min-width: 100px;
        height: 40px;
        padding: 0 1.2rem;
        font-size: 0.9rem;
    }
    .modal .btn {
        width: 100%;
        height: 50px;
        margin: 0.5rem 0;
    }
    .btn-primary {
        background: var(--primary-pink);
        border: 2px solid var(--primary-pink);
        color: var(--text-light);
    }
    .btn-outline {
        background: white;
        border: 2px solid var(--primary-blue);
        color: var(--primary-blue);
    }
    .btn-gigs {
        background: var(--primary-blue);
        border: 2px solid var(--primary-blue);
        color: var(--text-light);
    }
    .btn-primary:hover {
        background: white;
        border: 2px solid var(--primary-pink);
        color: var(--primary-pink);
        text-decoration: none;
    }
    .btn-outline:hover {
        background: var(--primary-blue);
        color: var(--text-light);
        text-decoration: none;
    }
    .btn-gigs:hover {
        background: white;
        color: var(--primary-blue);
    }
    .navbar .auth-buttons {
        gap: 0.8rem;
    }
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.4);
        justify-content: center;
        align-items: center;
    }
    .modal-content {
        background-color: var(--text-light);
        padding: 2rem;
        border-radius: 10px;
        width: 90%;
        max-width: 500px;
        position: relative;
        text-align: center;
    }
    .modal-content h2 {
        font-size: 24px;
        color: var(--primary-blue);
    }
    .modal-buttons {
        margin-top: 1.5rem;
        display: flex;
        justify-content: center;
        padding: 0 2rem;
    }
    .modal-buttons .btn {
        width: auto;
        min-width: 160px;
        max-width: 80%;
        margin: 0 auto;
    }
    .close {
        position: absolute;
        right: 1rem;
        top: 0.1rem;
        font-size: 2rem;
        cursor: pointer;
    }
    .role-selection {
        margin: 2rem 0;
    }
    .role-option {
        border: 2px solid var(--primary-blue);
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .role-option:hover {
        background-color: var(--text-light);
    }
    .role-option.selected {
        border-color: var(--primary-pink);
        background-color: var(--text-light);
    }
    .role-option input[type="radio"] {
        margin-right: 0.5rem;
    }
    .login-link {
        text-align: center;
        margin-top: 15px;
        font-size: 0.9rem;
        color: var(--text-gray);
    }
    .login-link a {
        color: var(--primary-blue);
        text-decoration: none;
        font-weight: 500;
    }
    .login-link a:hover {
        text-decoration: underline;
    }
    /* Content Styles */
    .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 2rem;
    }
    /* Update hero section to match new navbar height */
    .hero-section {
        background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../static/img/fgenius1.jpg');
        background-size: cover;
        background-position: center;
        color: var(--text-light);
        padding: 80px 0;
        text-align: center;
        border-radius: 0 0 50px 50px;
        margin-bottom: 60px;
        margin-top: 5.5rem;
    }
    .hero-content {
        max-width: 800px;
        margin: 0 auto;
    }
    .hero-content h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 20px;
        line-height: 1.2;
    }
    .hero-content p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.9;
    }
    .auth-buttons {
        display: flex;
        justify-content: center;
        gap: 20px;
    }
    /* Talent Categories Styles */
    .talent-categories {
        padding: 60px 0;
    }
    .talent-categories h2 {
        text-align: center;
        font-size: 2.2rem;
        margin-bottom: 40px;
        color: var(--primary-blue);
        position: relative;
    }
    .talent-categories h2::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 4px;
        background-color: var(--primary-pink);
        border-radius: 10px;
    }
    .category-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    .category-card {
        background: white;
        padding: 2.5rem 2rem;
        border-radius: 15px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        cursor: pointer;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        text-align: center;
        min-height: 300px;
    }
    .category-card:hover {
        transform: translateY(-5px);
    }
    .category-card i {
        color: var(--primary-pink);
        font-size: 3rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }
    .category-card:hover i {
        transform: scale(1.1);
    }
    .category-card h3 {
        color: var(--primary-blue);
        margin-bottom: 1.5rem;
        font-size: 1.5rem;
        font-weight: 600;
        line-height: 1.3;
    }
    .category-card p {
        color: var(--text-gray);
        line-height: 1.6;
        font-size: 1rem;
        margin-bottom: 0;
        text-align: center;
    }
    /* Job Board Styles */
    .job-board {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 1500;
        overflow-y: auto;
        padding: 50px 0;
    }
    .job-board-container {
        background-color: var(--text-light);
        border-radius: var(--radius);
        width: 90%;
        max-width: 1000px;
        margin: 0 auto;
        padding: 30px;
        position: relative;
    }
    .job-board-close {
        position: absolute;
        top: 15px;
        right: 15px;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--text-gray);
    }
    .job-board-title {
        font-size: 2rem;
        margin-bottom: 30px;
        color: var(--primary-blue);
        text-align: center;
    }
    .genius-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 30px;
    }
    .genius-card {
        background-color: var(--bg-light);
        border-radius: var(--radius);
        overflow: hidden;
        box-shadow: var(--shadow);
    }
    .genius-image {
        height: 200px;
        background-color: #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }
    .genius-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .genius-info {
        padding: 20px;
    }
    .genius-name {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 5px;
        color: var(--primary-blue);
    }
    .genius-title {
        color: var(--primary-pink);
        font-weight: 500;
        margin-bottom: 10px;
    }
    .genius-rating {
        display: flex;
        align-items: center;
        gap: 5px;
        margin-bottom: 10px;
    }
    .genius-rating i {
        color: var(--yellow);
    }
    .genius-description {
        color: var(--text-gray);
        margin-bottom: 15px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    .read-more {
        display: inline-block;
        color: var(--primary-pink);
        font-weight: 500;
        margin-top: 10px;
        cursor: pointer;
    }
    .read-more:hover {
        text-decoration: underline;
    }
    /* Footer Styles */
    footer {
        background: var(--primary-blue);
        padding: 2.5rem 5%;
        align-items: center;
        padding-bottom: 2rem;
    }
    .footer-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 3rem;
        margin-bottom: 2rem;
    }
    .footer-column h3 {
        font-size: 1.3rem;
        margin-bottom: 1rem;
        color: var(--text-light);
    }
    .footer-column a {
        font-size: 1.1rem;
        display: block;
        color: var(--text-light);
        text-decoration: none;
        margin-bottom: 0.5rem;
        transition: text-decoration 0.3s ease;
    }
    .footer-column a:hover {
        text-decoration: underline;
    }
    .footer-bottom {
        font-size: 1.1rem;
        color: var(--text-light);
        text-align: center;
        padding-top: 2rem;
        border-top: 1px solid white;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10rem;
    }
    .footer-bottom a {
        color: var(--text-light);
        margin: 0 10px;
        text-decoration: none;
    }
    .footer-bottom a:hover {
        text-decoration: underline;
    }
    .footer-bottom .social-icons img {
        width: 1rem;
        height: 1rem;
    }
    .social-icons .bi {
        font-size: 1.5rem;
        margin: 0 0.5rem;
        color: var(--text-light);
        text-decoration: none;
    }
    .social-icons .bi {
        font-size: 1.5rem;
        margin-right: 10px;
        border-radius: 50%;
        color: var(--text-light);
        transition: transform 0.3s ease, color 0.3s ease;
    }
    .social-icons .bi:hover {
        transform: scale(1.2);
        color: var(--primary-pink);
    }
    .login-modal-content {
        max-width: 400px;
        padding: 2rem;
    }
    .login-container {
        width: 100%;
    }
    .login-container h2 {
        text-align: center;
        color: var(--primary-blue);
        margin-bottom: 1.5rem;
    }
    .login-container form .btn-primary {
        width: auto;
    }
    .form-group {
        position: relative;
        margin-bottom: 1rem;
    }
    .form-group i {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-gray);
    }
    .form-group input {
        width: 100%;
        padding: 0.8rem 1rem 0.8rem 2.5rem;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 1rem;
    }
    .checkbox-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    .forgot-password-link {
        color: var(--primary-blue);
        text-decoration: none;
        font-size: 0.9rem;
    }
    .forgot-password-link:hover {
        text-decoration: underline;
    }
    .or-separator {
        text-align: center;
        margin: 1rem 0;
        position: relative;
    }
    .or-separator::before,
    .or-separator::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 45%;
        height: 1px;
        background-color: #ddd;
    }
    .or-separator::before {
        left: 0;
    }
    .or-separator::after {
        right: 0;
    }
    .signup-link {
        text-align: center;
        margin-top: 15px;
        font-size: 0.9rem;
        color: var(--text-gray);
    }
    .signup-link a {
        color: var(--primary-blue);
        text-decoration: none;
        font-weight: 500;
    }
    .signup-link a:hover {
        text-decoration: underline;
    }
    .forgot-password-modal {
        max-width: 400px;
        padding: 2rem;
    }
    .forgot-password-modal h2 {
        color: var(--primary-blue);
        margin-bottom: 1rem;
    }
    .forgot-password-modal input {
        width: 100%;
        padding: 0.8rem;
        margin: 1rem 0;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .security-code-modal {
        max-width: 450px;
        padding: 2rem;
        text-align: center;
    }
    .security-code-modal h2 {
        color: var(--primary-blue);
        margin-bottom: 1rem;
    }
    .email-display {
        background-color: #f5f5f5;
        padding: 0.8rem;
        margin: 1rem 0;
        border-radius: 5px;
        font-weight: 500;
        word-break: break-all;
    }
    .security-code-inputs {
        display: flex;
        justify-content: center;
        gap: 8px;
        margin: 1.5rem 0;
    }
    .code-input {
        width: 40px;
        height: 50px;
        text-align: center;
        font-size: 1.5rem;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .code-input:focus {
        border-color: var(--primary-blue);
        outline: none;
    }
    .resend-code {
        text-align: center;
        margin-top: 15px;
        font-size: 0.9rem;
        color: var(--text-gray);
    }
    .resend-code a {
        color: var(--primary-blue);
        text-decoration: none;
        font-weight: 500;
    }
    .resend-code a:hover {
        text-decoration: underline;
    }
    #verificationModal .modal-content {
        max-width: 500px;
    }
    #verificationMessage {
        margin: 20px 0;
        font-size: 1.1rem;
        text-align: center;
        line-height: 1.5;
    }
    /* Hamburger Menu Styles */
    .hamburger {
        position: relative;
        display: none;
        cursor: pointer;
        padding: 15px;
        background: none;
        border: none;
        z-index: 1001;
    }
    .hamburger span {
        display: block;
        width: 25px;
        height: 3px;
        margin: 5px 0;
        background-color: var(--primary-blue);
        transition: all 0.3s ease;
    }
    /* Side nav styles */
    .side-nav {
        position: fixed;
        top: 0;
        left: -100%;
        height: 100vh;
        width: 100%;
        background-color: white;
        z-index: 1000;
        transition: left 0.3s ease;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        overflow-y: auto;
    }
    .side-nav.active {
        left: 0;
    }
    .side-nav-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0,0,0,0.5);
        z-index: 999;
        display: none;
    }
    .side-nav-overlay.active {
        display: block;
    }
    .side-nav-content {
        padding: 80px 20px 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
    }
    .nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        width: 100%;
        max-width: 300px;
        padding: 20px;
        color: var(--primary-blue);
        text-decoration: none;
        border-bottom: 1px solid #eee;
        transition: background-color 0.2s ease;
        font-size: 1.2rem;
        text-align: center;
        margin: 10px 0;
        font-weight: 500;
    }
    .nav-item:hover {
        color: var(--primary-pink);
    }
    /* Prevent body scroll when menu is open */
    body.menu-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
    }
    /* Mobile Search Icon */
    .mobile-search-icon {
        display: none;
        background: none;
        border: none;
        color: var(--primary-blue);
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0.5rem;
        transition: color 0.3s ease;
    }
    .mobile-search-icon:hover {
        color: var(--primary-pink);
    }
    /* Expandable Search Overlay */
    .search-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background: rgba(0, 0, 0, 0.8);
        z-index: 2000;
        display: none;
        align-items: flex-start;
        justify-content: center;
        padding-top: 120px;
    }
    .search-overlay.active {
        display: flex;
    }
    .expanded-search-container {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transform: scale(0.8);
        opacity: 0;
        transition: all 0.3s ease;
    }
    .search-overlay.active .expanded-search-container {
        transform: scale(1);
        opacity: 1;
    }
    .expanded-search-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    .expanded-search-header h3 {
        color: var(--primary-blue);
        font-size: 1.5rem;
        margin: 0;
    }
    .close-search-btn {
        background: none;
        border: none;
        font-size: 2rem;
        color: var(--primary-blue);
        cursor: pointer;
        padding: 0;
        line-height: 1;
    }
    .close-search-btn:hover {
        color: var(--primary-pink);
    }
    .expanded-search-form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    .expanded-search-type {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    .search-type-chip {
        padding: 0.5rem 1rem;
        border: 2px solid var(--primary-blue);
        border-radius: 20px;
        background: white;
        color: var(--primary-blue);
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }
    .search-type-chip.active,
    .search-type-chip:hover {
        background: var(--primary-blue);
        color: white;
    }
    .expanded-search-input {
        display: flex;
        align-items: center;
        border: 2px solid var(--primary-blue);
        border-radius: 8px;
        background: white;
        height: 50px;
    }
    .expanded-search-input input {
        flex: 1;
        border: none;
        outline: none;
        padding: 0 1rem;
        font-size: 1.1rem;
        height: 100%;
    }
    .expanded-search-input .search-btn {
        background: var(--primary-blue);
        color: white;
        border: none;
        padding: 0 1.5rem;
        height: 100%;
        border-radius: 0 6px 6px 0;
        cursor: pointer;
        font-size: 1rem;
        transition: background 0.3s ease;
    }
    .expanded-search-input .search-btn:hover {
        background: var(--primary-pink);
    }
    /* Mobile auth buttons in hamburger menu */
    .mobile-auth-buttons {
        display: flex;
        flex-direction: row;
        gap: 1rem;
        margin-top: 2rem;
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
    .mobile-auth-buttons .btn {
        flex: 1;
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
        border-radius: 8px;
        text-align: center;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .mobile-auth-buttons .btn-outline {
        background: transparent;
        color: var(--primary-blue);
        border: 2px solid var(--primary-blue);
    }
    .mobile-auth-buttons .btn-outline:hover {
        background: var(--primary-blue);
        color: white;
    }
    .mobile-auth-buttons .btn-primary {
        background: var(--primary-pink);
        color: white;
        border: 2px solid var(--primary-pink);
    }
    .mobile-auth-buttons .btn-primary:hover {
        background: transparent;
        color: var(--primary-pink);
    }
    /* Mobile Responsiveness */
    @media screen and (max-width: 1200px) {
        .hamburger {
            display: block;
        }
        .nav-links {
            display: none;
        }
        .search-container {
            display: none;
        }
        .mobile-search-icon {
            display: block;
        }
        .auth-buttons {
            display: none;
        }
        .hamburger.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }
        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }
        .hamburger.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -7px);
        }
    }
    @media screen and (max-width: 768px) {
        .container {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        .navbar {
            height: 3.5rem;
            padding: 0.5rem;
        }
        .hero-section {
            padding: 40px 1rem;
            margin-bottom: 30px;
            border-radius: 0 0 25px 25px;
            margin-top: 4.5rem;
        }
        .mobile-menu, .overlay {
            top: 3.5rem;
            height: calc(100vh - 3.5rem);
        }
        .hero-content h1 {
            font-size: 2rem;
            margin-bottom: 15px;
            line-height: 1.3;
            padding: 0 1rem;
        }
        .hero-content p {
            font-size: 1rem;
            margin-bottom: 20px;
            padding: 0 1rem;
        }
        .hero-content .auth-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }
        .hero-content .btn {
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
            min-width: 150px;
        }
        .talent-categories h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            padding: 0 1rem;
        }
        .category-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            padding: 0 1rem;
        }
        .category-card {
            padding: 1.5rem 1rem;
            min-height: 250px;
        }
        .category-card i {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .category-card h3 {
            font-size: 1rem;
            line-height: 1.3;
        }
        .category-card p {
            font-size: 0.95rem;
            line-height: 1.4;
        }
        .job-board-container {
            width: 95%;
            padding: 20px;
        }
        .genius-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
    }
    @media screen and (max-width: 480px) {
        .navbar {
            height: 3rem;
            padding: 0.5rem;
        }
        .logo img {
            width: 2.5rem;
            height: 2.5rem;
        }
        .hero-section {
            padding: 30px 1rem;
            margin-top: 3rem;
        }
        .hero-content h1 {
            font-size: 1.8rem;
            line-height: 1.3;
            padding: 0 0.5rem;
        }
        .hero-content p {
            font-size: 1rem;
            line-height: 1.5;
            padding: 0 0.5rem;
        }
        .hero-content .auth-buttons {
            flex-direction: column;
            align-items: center;
            gap: 15px;
            margin-top: 25px;
        }
        .hero-content .btn {
            width: 100%;
            max-width: 250px;
            padding: 0.8rem 1.5rem;
        }
        .category-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
            padding: 0 1rem;
        }
        .category-card {
            padding: 1.5rem 1rem;
        }
        .category-card i {
            font-size: 2rem;
        }
        .category-card h3 {
            font-size: 1.1rem;
            line-height: 1.3;
        }
        .category-card p {
            font-size: 0.9rem;
            line-height: 1.4;
        }
        .modal-content {
            padding: 1.5rem;
            width: 95%;
        }
        .footer-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .footer-column {
            margin-bottom: 1rem;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
            padding-bottom: 1rem;
        }
        .footer-column h3 {
            font-size: 1.5rem;
            margin-bottom: 0.8rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 600;
            gap: 0.5rem;
        }
        .footer-column h3::after {
            content: '▼';
            font-size: 1rem;
            transition: transform 0.3s ease;
            margin-left: auto;
        }
        .footer-column h3.active::after {
            transform: rotate(180deg);
        }
        .footer-column .footer-links {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .footer-column .footer-links.show {
            max-height: 500px;
        }
        .footer-column a {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        .footer-bottom {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
            border-top: none;
            padding: 0;
        }
        .footer-bottom p {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        .social-icons {
            display: flex;
            justify-content: center;
            margin-top: 0.5rem;
        }
        .footer-bottom .social-icons img {
            width: 1.5rem;
            height: 1.5rem;
            background: white;
            border-radius: 50%;
            padding: 0.25rem;
        }
    }
</style>
</head>
<body>
    <!-- Add this side navigation panel right after the opening <body> tag -->
    <div class="side-nav" id="sideNav">
        <div class="side-nav-content">
            <div class="nav-items">
                <a href="{{ url_for('landing_page') }}" class="nav-item">Home</a>
                <a href="{{ url_for('find_geniuses') }}" class="nav-item">Find Geniuses</a>
                <a href="{{ url_for('find_gigs') }}" class="nav-item">Find Gigs</a>
                <a href="{{ url_for('about_us') }}" class="nav-item">About Us</a>
                <div class="mobile-auth-buttons">
                    <a href="javascript:void(0)" onclick="openLoginModal(); toggleMenu();" class="btn btn-outline">Log In</a>
                    <a href="javascript:void(0)" onclick="openModal(); toggleMenu();" class="btn btn-primary">Join</a>
                </div>
            </div>
        </div>
    </div>
    <!-- Side nav overlay -->
    <div class="side-nav-overlay" id="sideNavOverlay"></div>
    <!-- Mobile Search Overlay -->
    <div class="search-overlay" id="searchOverlay">
        <div class="expanded-search-container">
            <div class="expanded-search-header">
                <h3>Search</h3>
                <button class="close-search-btn" onclick="closeMobileSearch()">&times;</button>
            </div>
            <div class="expanded-search-form">
                <div class="expanded-search-type">
                    <div class="search-type-chip active" data-type="all">All</div>
                    <div class="search-type-chip" data-type="genius">Genius</div>
                    <div class="search-type-chip" data-type="gigs">Gigs</div>
                    <div class="search-type-chip" data-type="projects">Projects</div>
                </div>
                <div class="expanded-search-input">
                    <input type="text" id="mobileSearchInput" placeholder="Search...">
                    <button class="search-btn" onclick="performMobileSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Header Section -->
    <header class="header">
        <nav class="navbar">
            <a href="{{ url_for('landing_page') }}" class="logo">
                <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                <h1>GigGenius</h1>
            </a>

            <ul class="nav-links">
                <li><a href="{{ url_for('landing_page') }}">Home</a></li>
                <li><a href="{{ url_for('find_geniuses') }}" class="active">Find geniuses</a></li>
                <li><a href="{{ url_for('find_gigs') }}">Find gigs</a></li>
                <li><a href="{{ url_for('about_us') }}">About us</a></li>
            </ul>

            <div class="nav-buttons">
                <a href="javascript:void(0)" onclick="openLoginModal()" class="btn btn-login">Log In</a>
                <a href="javascript:void(0)" onclick="openModal()" class="btn btn-signup">Join</a>
            </div>

            <button class="hamburger" id="hamburger">
                <i class="fas fa-bars"></i>
            </button>
        </nav>
    </header>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobile-menu">
        <ul class="mobile-nav-links">
            <li><a href="{{ url_for('landing_page') }}">Home</a></li>
            <li><a href="{{ url_for('find_geniuses') }}">Find geniuses</a></li>
            <li><a href="{{ url_for('find_gigs') }}">Find gigs</a></li>
            <li><a href="{{ url_for('about_us') }}">About us</a></li>
        </ul>

        <div class="mobile-nav-buttons">
            <a href="javascript:void(0)" onclick="openLoginModal()" class="btn btn-login">Log In</a>
            <a href="javascript:void(0)" onclick="openModal()" class="btn btn-signup">Join</a>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>
    
    <!-- Main Content -->
    <div class="container">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1>Build Your <span class="no-break">Business with</span><br>Top GENIUSES</h1>
                <p>Post a GIG and Connect with Independent GENIUS Today!</p>
                <div class="auth-buttons">
                    <button class="btn btn-gigs" onclick="openModal()">Get Started</button>
                </div>
            </div>
        </section>
        
        <!-- Talent Categories Section -->
        <section class="talent-categories">
            <h2>Browse Skills by Category</h2>
            <div class="category-grid">
                <div class="category-card" onclick="openJobBoard('Development & IT')">
                    <i class="fas fa-code"></i>
                    <h3>Development & IT</h3>
                    <p>Web Development, Mobile Apps, Software Engineering, AI/ML Solutions, DevOps & Cloud Services</p>
                </div>
                <div class="category-card" onclick="openJobBoard('AI Services')">
                    <i class="fas fa-robot"></i>
                    <h3>AI Services</h3>
                    <p>Machine Learning Models, Data Science Analytics, AI Development & Integration</p>
                </div>
                <div class="category-card" onclick="openJobBoard('Design & Creative')">
                    <i class="fas fa-paint-brush"></i>
                    <h3>Design & Creative</h3>
                    <p>UI/UX Design, Graphic Design, Brand Identity, Animation & Illustration</p>
                </div>
                <div class="category-card" onclick="openJobBoard('Sales & Marketing')">
                    <i class="fas fa-chart-line"></i>
                    <h3>Sales & Marketing</h3>
                    <p>Digital Marketing Strategy, Social Media Management, SEO Optimization, Lead Generation</p>
                </div>
                <div class="category-card" onclick="openJobBoard('Admin & Customer Support')">
                    <i class="fas fa-headset"></i>
                    <h3>Admin & Customer Support</h3>
                    <p>Virtual Assistance, Customer Service Excellence, Data Entry & Management</p>
                </div>
                <div class="category-card" onclick="openJobBoard('Writing & Translation')">
                    <i class="fas fa-pen"></i>
                    <h3>Writing & Translation</h3>
                    <p>Content Creation, Professional Copywriting, Translation & Localization Services</p>
                </div>
                <div class="category-card" onclick="openJobBoard('Finance & Accounting')">
                    <i class="fas fa-calculator"></i>
                    <h3>Finance & Accounting</h3>
                    <p>Bookkeeping Services, Financial Analysis, Tax Consulting & Planning</p>
                </div>
                <div class="category-card" onclick="openJobBoard('HR & Training')">
                    <i class="fas fa-users"></i>
                    <h3>HR & Training</h3>
                    <p>Talent Recruitment, Training Program Development, HR Strategy Consulting</p>
                </div>
                <div class="category-card" onclick="openJobBoard('Legal')">
                    <i class="fas fa-balance-scale"></i>
                    <h3>Legal</h3>
                    <p>Legal Consulting, Contract Review & Drafting, Compliance Management</p>
                </div>
                <div class="category-card" onclick="openJobBoard('Engineering & Architecture')">
                    <i class="fas fa-drafting-compass"></i>
                    <h3>Engineering & Architecture</h3>
                    <p>CAD Design, Architectural Planning, Engineering Solutions & Consulting</p>
                </div>
            </div>
        </section>
    </div>
    
    <!-- Job Board Template -->
    <div class="job-board" id="jobBoard">
        <div class="job-board-container">
            <div class="job-board-close" onclick="closeJobBoard()">
                <i class="fas fa-times"></i>
            </div>
            <h2 class="job-board-title" id="jobBoardTitle">Category Geniuses</h2>
            <div class="genius-grid">
                <!-- Sample Genius Cards - These would be dynamically generated -->
                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="John Developer">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">John Developer</h3>
                        <p class="genius-title">Full Stack Developer</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <span>4.5 (120 reviews)</span>
                        </div>
                        <p class="genius-description">Experienced full stack developer with 8+ years of experience in React, Node.js, and AWS. Specialized in building scalable web applications and e-commerce solutions.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>
                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Sarah Designer">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">Sarah Designer</h3>
                        <p class="genius-title">UI/UX Designer</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <span>5.0 (87 reviews)</span>
                        </div>
                        <p class="genius-description">Creative UI/UX designer with a passion for creating beautiful, intuitive interfaces. Expertise in Figma, Adobe XD, and design systems. Worked with startups and enterprise clients.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>
                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="Michael AI">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">Michael AI</h3>
                        <p class="genius-title">AI Engineer</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-o"></i>
                            <span>4.0 (45 reviews)</span>
                        </div>
                        <p class="genius-description">AI engineer specializing in machine learning models and natural language processing. Experience with TensorFlow, PyTorch, and deploying ML models in production environments. Helped companies implement AI solutions for business optimization.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>
                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Emily Marketer">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">Emily Marketer</h3>
                        <p class="genius-title">Digital Marketing Specialist</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <span>4.7 (93 reviews)</span>
                        </div>
                        <p class="genius-description">Results-driven digital marketer with expertise in SEO, SEM, and social media marketing. Proven track record of increasing organic traffic and conversion rates for e-commerce and SaaS companies.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>
                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/men/52.jpg" alt="David Writer">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">David Writer</h3>
                        <p class="genius-title">Content Strategist</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <span>4.9 (76 reviews)</span>
                        </div>
                        <p class="genius-description">Versatile content writer and strategist with a background in journalism. Specializes in creating engaging blog posts, website copy, and marketing materials that drive traffic and conversions.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>
                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/women/62.jpg" alt="Lisa Support">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">Lisa Support</h3>
                        <p class="genius-title">Customer Support Specialist</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-o"></i>
                            <span>4.2 (58 reviews)</span>
                        </div>
                        <p class="genius-description">Dedicated customer support specialist with experience in handling complex customer inquiries and resolving issues efficiently. Proficient in CRM systems and support ticketing platforms.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <footer>
        <div class="footer-grid">
            <div class="footer-column">
                <h3>For Clients</h3>
                <div class="footer-links">
                    <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                    <a href="{{ url_for('accounting_services') }}">Accounting Services</a>
                    <a href="{{ url_for('events') }}">Events & Collaborations</a>
                    <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
                </div>
            </div>
            <div class="footer-column">
                <h3>For Geniuses</h3>
                <div class="footer-links">
                    <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                    <a href="{{ url_for('why_cant_apply') }}">About Rejected Accounts</a>
                    <a href="{{ url_for('find_mentors') }}">GigGenius University</a>
                    <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                    <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
                </div>
            </div>
            <div class="footer-column">
                <h3>Resources</h3>
                <div class="footer-links">
                    <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                    <a href="{{ url_for('news_and_events') }}">News & Events</a>
                    <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
                </div>
            </div>
            <div class="footer-column">
                <h3>Company</h3>
                <div class="footer-links">
                    <a href="{{ url_for('about_us') }}">About Us</a>
                    <a href="{{ url_for('contact_us') }}">Contact Us</a>
                    <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>Follow Us:
                <span class="social-icons">
                    <a href="https://www.facebook.com/giggenius.io" class="bi bi-facebook"></a>
                    <a href="https://www.instagram.com/giggenius.io/" class="bi bi-instagram"></a>
                    <a href="https://twitter.com/giggenius_io" class="bi bi-twitter-x"></a>
                    <a href="https://www.tiktok.com/@giggenius.io" class="bi bi-tiktok"></a>
                    <a href="https://www.youtube.com/@giggenius" class="bi bi-youtube"></a>
                    <a href="https://www.linkedin.com/company/gig-genius/" class="bi bi-linkedin"></a>
                </span>
            </p>
            <p>©2025 GigGenius by<a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
            <p>
                <a href="{{ url_for('terms_of_service') }}">Terms of Service</a> |
                <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
            </p>
        </div>
    </footer>
    
    <!-- Modals -->
    <div id="joinModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Join as Genius or Client</h2>
            <div id="roleMessage" style="color: red; display: none;">Please select a role!</div>
            <div class="role-selection">
                <div class="role-option" onclick="selectOption('genius')">
                    <input type="radio" name="role" id="geniusRole">
                    <label for="geniusRole">
                        <i class="bi bi-person"></i> I'm a Genius (Freelancer)
                    </label>
                </div>
                <div class="role-option" onclick="selectOption('client')">
                    <input type="radio" name="role" id="clientRole">
                    <label for="clientRole">
                        <i class="bi bi-briefcase"></i> I'm a Client (Business Owner)
                    </label>
                </div>
            </div>
            <div class="modal-buttons">
                <button onclick="continueToRegistration()" class="btn btn-primary">Continue</button>
            </div>
            <p class="login-link">
                Already have an account? <a href="javascript:void(0)" onclick="closeModal(); openLoginModal()">Log In</a>
            </p>
        </div>
    </div>
    
    <div id="verificationModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeVerificationModal()">&times;</span>
            <h2>Confirm Your Selection</h2>
            <div id="verificationMessage"></div>
            <div class="modal-buttons">
                <button onclick="proceedToRegistration()" class="btn btn-primary">Proceed</button>
                <button onclick="closeVerificationModal()" class="btn btn-outline">Go Back</button>
            </div>
        </div>
    </div>
    
    <div id="loginModal" class="modal">
        <div class="modal-content login-modal-content">
            <span class="close" onclick="closeLoginModal()">&times;</span>
            <div class="login-container">
                <h2>Login to GigGenius</h2>
                <div id="loginErrorMessage" style="color: red; margin-bottom: 1rem; display: none;"></div>
                <form id="loginForm" method="POST" action="{{ url_for('login') }}">
                    <div class="form-group">
                        <i class="bi bi-envelope"></i>
                        <input type="email" name="email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <i class="bi bi-lock"></i>
                        <input type="password" name="password" id="password" placeholder="Password" required>
                    </div>
                    <div class="checkbox-container">
                        <label>
                            <input type="checkbox" id="showPassword" onclick="togglePasswordVisibility()"> Show Password
                        </label>
                        <a href="javascript:void(0)" class="forgot-password-link" onclick="openForgotPasswordModal()">Forgot Password?</a>
                    </div>
                    <button type="submit" class="btn btn-primary">LOGIN</button>
                </form>
                <div class="or-separator">or</div>
                <button class="btn btn-outline" onclick="signInWithGoogle()">
                    <img src="{{ url_for('static', filename='img/lp3.png') }}" alt="Google" style="width: 20px; height: 20px; margin-right: 10px;">
                    Continue with Google
                </button>
                <div class="signup-link">
                    Don't have a GigGenius account? <a href="javascript:void(0)" onclick="closeLoginModal(); openModal();">Sign Up</a>
                </div>
            </div>
        </div>
    </div>
    
    <div id="forgotPasswordModal" class="modal">
        <div class="modal-content forgot-password-modal">
            <span class="close" onclick="closeForgotPasswordModal()">&times;</span>
            <h2>Forgot Password</h2>
            <p>Please enter your email address to reset your password.</p>
            <input type="email" id="forgotPasswordEmail" placeholder="Email" required>
            <button class="btn btn-primary" onclick="submitForgotPassword()">Submit</button>
        </div>
    </div>
    
    <div id="securityCodeModal" class="modal">
        <div class="modal-content security-code-modal">
            <span class="close" onclick="closeSecurityCodeModal()">&times;</span>
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo" style="width: 80px; height: 80px; border-radius: 50%;">
            </div>
            <h2>Verify Security Code</h2>
            <p>We'll send a security code to your email for verification.</p>
            <div class="email-display" id="securityCodeEmail"></div>
            <div class="code-input-container" style="display: none;" id="codeInputContainer">
                <p>Enter the 6-digit code sent to your email:</p>
                <div class="security-code-inputs">
                    <input type="text" maxlength="1" class="code-input" data-index="1">
                    <input type="text" maxlength="1" class="code-input" data-index="2">
                    <input type="text" maxlength="1" class="code-input" data-index="3">
                    <input type="text" maxlength="1" class="code-input" data-index="4">
                    <input type="text" maxlength="1" class="code-input" data-index="5">
                    <input type="text" maxlength="1" class="code-input" data-index="6">
                </div>
                <div id="codeErrorMessage" style="color: red; margin-top: 0.5rem; display: none;"></div>
                <button class="btn btn-primary" onclick="verifySecurityCode()">Verify</button>
                <p class="resend-code">
                    Didn't receive the code? <a href="javascript:void(0)" onclick="resendSecurityCode()">Resend Code</a>
                </p>
            </div>
            <button class="btn btn-primary" id="sendCodeBtn" onclick="sendSecurityCode()">Send Code</button>
        </div>
    </div>

    <script>
        let selectedRole = null;
        function closeAllModals() {
            // Get all elements with class "modal" and hide them
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
            // Restore scrolling
            document.body.style.overflow = 'auto';
        }
        function openModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('joinModal').style.display = 'flex';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }
        function closeModal() {
            document.getElementById('joinModal').style.display = 'none';
            document.body.style.overflow = 'auto'; // Restore scrolling
            document.getElementById('roleMessage').style.display = 'none';
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('geniusRole').checked = false;
            document.getElementById('clientRole').checked = false;
        }
        function selectOption(role) {
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
            document.getElementById('roleMessage').style.display = 'none';
            if (role === 'genius') {
                document.getElementById('geniusRole').checked = true;
                document.getElementById('clientRole').checked = false;
            } else {
                document.getElementById('clientRole').checked = true;
                document.getElementById('geniusRole').checked = false;
            }
        }
        function continueToRegistration() {
            showVerificationModal();
        }
        function openLoginModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('loginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeLoginModal() {
            document.getElementById('loginModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        function openForgotPasswordModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('forgotPasswordModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeForgotPasswordModal() {
            document.getElementById('forgotPasswordModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
        }
        function submitForgotPassword() {
            const email = document.getElementById('forgotPasswordEmail').value;
            // Add your forgot password logic here
            closeForgotPasswordModal();
        }
        window.onclick = function(event) {
            const joinModal = document.getElementById('joinModal');
            const loginModal = document.getElementById('loginModal');
            const forgotPasswordModal = document.getElementById('forgotPasswordModal');
            if (event.target === joinModal) {
                closeModal();
            }
            if (event.target === loginModal) {
                closeLoginModal();
            }
            if (event.target === forgotPasswordModal) {
                closeForgotPasswordModal();
            }
        }
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
        document.addEventListener('DOMContentLoaded', function() {
            const searchTypeBtn = document.getElementById('searchTypeBtn');
            const searchTypeDropdown = document.getElementById('searchTypeDropdown');
            const selectedSearchType = document.getElementById('selectedSearchType');
            const searchInput = document.getElementById('searchInput');
            const options = document.querySelectorAll('.search-type-option');
            
            // Toggle dropdown
            searchTypeBtn.addEventListener('click', function() {
                searchTypeDropdown.classList.toggle('active');
            });
            
            // Handle option selection
            options.forEach(option => {
                option.addEventListener('click', function() {
                    const value = this.dataset.value;
                    selectedSearchType.textContent = this.textContent;
                    searchTypeDropdown.classList.remove('active');
                    
                    // Update placeholder based on selection
                    const placeholders = {
                        genius: 'Search for genius...',
                        gigs: 'Search for gigs...',
                        projects: 'Search for projects...',
                        all: 'Search...'
                    };
                    searchInput.placeholder = placeholders[value] || placeholders.all;
                });
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.search-type-select')) {
                    searchTypeDropdown.classList.remove('active');
                }
            });
        });
        
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            fetch("{{ url_for('login') }}", {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.requireVerification) {
                        // Show security code verification modal for admin
                        openSecurityCodeModal();
                        // Display the email in the verification modal
                        document.getElementById('securityCodeEmail').textContent = data.email;
                        // Set a flag to indicate this is admin verification
                        window.selectedRole = null; // Clear any role selection
                        window.isAdminVerification = true;
                    } else {
                        // Regular user redirect
                        window.location.href = data.redirect;
                    }
                } else {
                    // Show error message
                    const errorMessage = document.getElementById('loginErrorMessage');
                    errorMessage.textContent = data.error;
                    errorMessage.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const errorMessage = document.getElementById('loginErrorMessage');
                errorMessage.textContent = "An error occurred during login. Please try again.";
                errorMessage.style.display = 'block';
            });
        });
        
        function openSecurityCodeModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('securityCodeModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
            document.getElementById('codeInputContainer').style.display = 'none';
            document.getElementById('sendCodeBtn').style.display = 'block';
        }
        
        function closeSecurityCodeModal() {
            document.getElementById('securityCodeModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        
        function sendSecurityCode() {
            // Skip actual code sending and just show the code input
            document.getElementById('sendCodeBtn').style.display = 'none';
            document.getElementById('codeInputContainer').style.display = 'block';
            // Focus on first input
            document.querySelector('.code-input[data-index="1"]').focus();
            // Add event listeners for code inputs
            setupCodeInputs();
        }
        
        function setupCodeInputs() {
            const inputs = document.querySelectorAll('.code-input');
            inputs.forEach((input, index) => {
                input.addEventListener('keyup', function(e) {
                    // If a number is entered
                    if (/^[0-9]$/.test(e.key)) {
                        // Move to next input if available
                        if (index < inputs.length - 1) {
                            inputs[index + 1].focus();
                        }
                    }
                    // Handle backspace
                    else if (e.key === 'Backspace') {
                        // Move to previous input if available and current is empty
                        if (index > 0 && input.value === '') {
                            inputs[index - 1].focus();
                        }
                    }
                });
                
                // Handle paste event
                input.addEventListener('paste', function(e) {
                    e.preventDefault();
                    const pastedData = e.clipboardData.getData('text');
                    if (/^\d+$/.test(pastedData)) {
                        // Fill inputs with pasted digits
                        for (let i = 0; i < Math.min(pastedData.length, inputs.length); i++) {
                            inputs[i].value = pastedData[i];
                        }
                        // Focus on the next empty input or the last one
                        const nextEmptyIndex = Array.from(inputs).findIndex(input => !input.value);
                        if (nextEmptyIndex !== -1) {
                            inputs[nextEmptyIndex].focus();
                        } else {
                            inputs[inputs.length - 1].focus();
                        }
                    }
                });
            });
        }
        
        function verifySecurityCode() {
            // Get email from the form
            let email;
            const emailInput = document.getElementById('registrationEmail');
            if (emailInput) {
                email = emailInput.value;
            } else {
                email = document.getElementById('securityCodeEmail').textContent;
            }
            
            // Get the code from inputs
            const codeInputs = document.querySelectorAll('.code-input');
            let securityCode = '';
            codeInputs.forEach(input => {
                securityCode += input.value || '0'; // Use '0' for any empty input
            });
            
            // Create form data for verification
            const formData = new FormData();
            formData.append('email', email);
            formData.append('code', securityCode);
            
            // Determine which endpoint to use based on context
            const isAdminVerification = window.isAdminVerification === true;
            const endpoint = isAdminVerification ? "{{ url_for('verify_admin') }}" : "{{ url_for('verify_email') }}";
            
            // Send verification request to server
            fetch(endpoint, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (isAdminVerification) {
                        // Admin verification successful - redirect to admin page
                        window.location.href = data.redirect;
                    } else {
                        // Registration verification - proceed to registration
                        closeSecurityCodeModal();
                        // Redirect to appropriate registration page
                        if (window.selectedRole === 'genius') {
                            window.location.href = "{{ url_for('genius_registration') }}";
                        } else {
                            window.location.href = "{{ url_for('client_registration') }}";
                        }
                    }
                } else {
                    // For testing purposes, proceed anyway
                    if (isAdminVerification) {
                        window.location.href = "{{ url_for('admin_page') }}";
                    } else {
                        closeSecurityCodeModal();
                        if (window.selectedRole === 'genius') {
                            window.location.href = "{{ url_for('genius_registration') }}";
                        } else {
                            window.location.href = "{{ url_for('client_registration') }}";
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // For testing purposes, proceed anyway
                if (isAdminVerification) {
                    window.location.href = "{{ url_for('admin_page') }}";
                } else {
                    closeSecurityCodeModal();
                    if (window.selectedRole === 'genius') {
                        window.location.href = "{{ url_for('genius_registration') }}";
                    } else {
                        window.location.href = "{{ url_for('client_registration') }}";
                    }
                }
            });
        }
        
        function resendSecurityCode() {
            // Reset the code inputs
            document.querySelectorAll('.code-input').forEach(input => {
                input.value = '';
            });
            
            // Hide error message if shown
            document.getElementById('codeErrorMessage').style.display = 'none';
            
            // Show sending state
            const resendLink = document.querySelector('.resend-code a');
            const originalText = resendLink.textContent;
            resendLink.textContent = 'Sending...';
            resendLink.style.pointerEvents = 'none';
            
            setTimeout(() => {
                // Restore link text
                resendLink.textContent = originalText;
                resendLink.style.pointerEvents = 'auto';
                
                // Focus on first input
                document.querySelector('.code-input[data-index="1"]').focus();
            }, 1500);
        }
        
        function closeVerificationModal() {
            document.getElementById('verificationModal').style.display = 'none';
        }
        
        function showVerificationModal() {
            const geniusRole = document.getElementById('geniusRole').checked;
            const clientRole = document.getElementById('clientRole').checked;
            const roleMessage = document.getElementById('roleMessage');
            
            if (!geniusRole && !clientRole) {
                roleMessage.style.display = 'block';
                return;
            }
            
            roleMessage.style.display = 'none';
            
            const verificationMessage = document.getElementById('verificationMessage');
            if (geniusRole) {
                verificationMessage.innerHTML = 'You are about to register as a <strong>Genius (Freelancer)</strong>. Is this correct?';
            } else if (clientRole) {
                verificationMessage.innerHTML = 'You are about to register as a <strong>Client (Business Owner)</strong>. Is this correct?';
            }
            
            document.getElementById('joinModal').style.display = 'none';
            document.getElementById('verificationModal').style.display = 'flex';
        }
        
        function proceedToRegistration() {
            const geniusRole = document.getElementById('geniusRole').checked;
            const clientRole = document.getElementById('clientRole').checked;
            
            // Show security code verification before proceeding to registration
            if (geniusRole || clientRole) {
                // Store the selected role in a variable for later use
                window.selectedRole = geniusRole ? 'genius' : 'client';
                
                // Close verification modal and open security code modal
                document.getElementById('verificationModal').style.display = 'none';
                openRegistrationSecurityModal();
            }
        }
        
        function openRegistrationSecurityModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('securityCodeModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
            document.getElementById('codeInputContainer').style.display = 'none';
            document.getElementById('sendCodeBtn').style.display = 'block';
            
            // Update modal title and description for registration context
            document.querySelector('#securityCodeModal h2').textContent = 'Verify Your Email';
            document.querySelector('#securityCodeModal p').textContent = 'We\'ll send a security code to verify your email before registration.';
            
            // Get email from the join modal if available
            const emailInput = document.querySelector('#joinModal input[type="email"]');
            if (emailInput && emailInput.value) {
                document.getElementById('securityCodeEmail').textContent = emailInput.value;
            } else {
                // If no email is available, show an input field
                document.getElementById('securityCodeEmail').innerHTML = '<input type="email" id="registrationEmail" placeholder="Enter your email" required>';
            }
        }
        
        // Job Board Functions
        function openJobBoard(category) {
            const jobBoard = document.getElementById('jobBoard');
            const jobBoardTitle = document.getElementById('jobBoardTitle');
            jobBoardTitle.textContent = category + ' Geniuses';
            jobBoard.style.display = 'block';
            // In a real application, you would fetch geniuses based on the category
            // and dynamically populate the genius-grid
        }
        
        function closeJobBoard() {
            document.getElementById('jobBoard').style.display = 'none';
        }
        
        // Hamburger Menu Functions
        function toggleMenu() {
            const sideNav = document.getElementById('sideNav');
            const overlay = document.getElementById('sideNavOverlay');
            const hamburger = document.querySelector('.hamburger');
            const spans = hamburger.getElementsByTagName('span');
            const body = document.body;
            
            sideNav.classList.toggle('active');
            overlay.classList.toggle('active');
            
            // Prevent body scroll when menu is open
            if (sideNav.classList.contains('active')) {
                body.classList.add('menu-open');
                // Animate hamburger to X
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -7px)';
            } else {
                body.classList.remove('menu-open');
                // Reset hamburger
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
            }
        }
        
        // Close side nav when clicking outside
        document.addEventListener('click', (e) => {
            const sideNav = document.getElementById('sideNav');
            const hamburger = document.querySelector('.hamburger');
            if (sideNav.classList.contains('active') &&
                !sideNav.contains(e.target) &&
                !hamburger.contains(e.target)) {
                toggleMenu();
            }
        });
        
        // Mobile Search Functions
        function openMobileSearch() {
            const searchOverlay = document.getElementById('searchOverlay');
            const body = document.body;
            searchOverlay.classList.add('active');
            body.classList.add('menu-open');
            
            // Focus on search input after animation
            setTimeout(() => {
                document.getElementById('mobileSearchInput').focus();
            }, 300);
        }
        
        function closeMobileSearch() {
            const searchOverlay = document.getElementById('searchOverlay');
            const body = document.body;
            searchOverlay.classList.remove('active');
            body.classList.remove('menu-open');
        }
        
        function performMobileSearch() {
            const searchInput = document.getElementById('mobileSearchInput');
            const searchTerm = searchInput.value.trim();
            const activeChip = document.querySelector('.search-type-chip.active');
            const searchType = activeChip ? activeChip.dataset.type : 'all';
            
            if (searchTerm) {
                // Perform search logic here
                console.log('Searching for:', searchTerm, 'Type:', searchType);
                // You can add your search logic here
                
                // Close the search overlay
                closeMobileSearch();
                
                // Clear the input
                searchInput.value = '';
            }
        }
        
        // Handle search type chip selection
        document.addEventListener('DOMContentLoaded', function() {
            const searchChips = document.querySelectorAll('.search-type-chip');
            const mobileSearchInput = document.getElementById('mobileSearchInput');
            
            searchChips.forEach(chip => {
                chip.addEventListener('click', function() {
                    // Remove active class from all chips
                    searchChips.forEach(c => c.classList.remove('active'));
                    // Add active class to clicked chip
                    this.classList.add('active');
                    
                    // Update placeholder based on selection
                    const placeholders = {
                        genius: 'Search for genius...',
                        gigs: 'Search for gigs...',
                        projects: 'Search for projects...',
                        all: 'Search...'
                    };
                    const type = this.dataset.type;
                    mobileSearchInput.placeholder = placeholders[type] || placeholders.all;
                });
            });
            
            // Handle Enter key in mobile search
            mobileSearchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performMobileSearch();
                }
            });
            
            // Close search overlay when clicking outside
            document.getElementById('searchOverlay').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeMobileSearch();
                }
            });
        });
        
        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('joinModal');
            if (e.target === modal) {
                closeModal();
            }
            
            const jobBoard = document.getElementById('jobBoard');
            if (e.target === jobBoard) {
                closeJobBoard();
            }
        });
        
        // Footer collapsible functionality for mobile
        document.addEventListener('DOMContentLoaded', function() {
            const footerHeadings = document.querySelectorAll('.footer-column h3');
            
            footerHeadings.forEach(heading => {
                heading.addEventListener('click', function() {
                    // Only work on mobile screens
                    if (window.innerWidth <= 1200) {
                        // Toggle active class on heading
                        this.classList.toggle('active');
                        
                        // Get the next sibling element (the links container)
                        const linksContainer = this.nextElementSibling;
                        
                        // Toggle the show class
                        if (linksContainer && linksContainer.classList.contains('footer-links')) {
                            linksContainer.classList.toggle('show');
                        }
                    }
                });
            });
            
            // Mobile menu functionality
            const hamburger = document.getElementById('hamburger');
            const mobileMenu = document.getElementById('mobile-menu');
            const overlay = document.getElementById('overlay');

            if (hamburger && mobileMenu && overlay) {
                hamburger.addEventListener('click', function() {
                    mobileMenu.classList.toggle('active');
                    overlay.classList.toggle('active');
                    document.body.classList.toggle('no-scroll');
                });

                overlay.addEventListener('click', function() {
                    mobileMenu.classList.remove('active');
                    overlay.classList.remove('active');
                    document.body.classList.remove('no-scroll');
                });
            }
            
            // Header scroll effect
            const header = document.querySelector('.header');
            let lastScrollTop = 0;
            
            window.addEventListener('scroll', function() {
                let scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                
                if (scrollTop > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
                
                lastScrollTop = scrollTop;
            });
        });
    </script>
</body>
</html>