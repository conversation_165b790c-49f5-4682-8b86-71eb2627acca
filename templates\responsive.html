<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Website</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3a0ca3;
            --accent-color: #f72585;
            --text-light: #ffffff;
            --text-dark: #333333;
            --background-light: #f8f9fa;
            --transition-speed: 0.3s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-color: var(--background-light);
            overflow-x: hidden;
        }

        /* Header Styles */
        .header {
            background-color: var(--primary-color);
            color: var(--text-light);
            position: fixed;
            width: 100%;
            top: 0;
            left: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all var(--transition-speed) ease;
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 5%;
            max-width: 1400px;
            margin: 0 auto;
            height: 80px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo-icon {
            font-size: 2rem;
            color: var(--text-light);
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-light);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: var(--text-light);
            text-decoration: none;
            font-weight: 500;
            font-size: 1.1rem;
            transition: color var(--transition-speed) ease;
            position: relative;
        }

        .nav-links a:hover {
            color: var(--accent-color);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 0;
            background-color: var(--accent-color);
            transition: width var(--transition-speed) ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.6rem 1.5rem;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-speed) ease;
            border: none;
            font-size: 1rem;
        }

        .btn-login {
            background-color: transparent;
            color: var(--text-light);
            border: 2px solid var(--text-light);
        }

        .btn-login:hover {
            background-color: var(--text-light);
            color: var(--primary-color);
        }

        .btn-signup {
            background-color: var(--accent-color);
            color: var(--text-light);
        }

        .btn-signup:hover {
            background-color: #d91a6d;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(247, 37, 133, 0.3);
        }

        .hamburger {
            display: none;
            cursor: pointer;
            background: none;
            border: none;
            color: var(--text-light);
            font-size: 1.8rem;
        }

        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            top: 80px;
            right: -100%;
            width: 80%;
            max-width: 400px;
            height: calc(100vh - 80px);
            background-color: var(--primary-color);
            transition: right var(--transition-speed) ease;
            z-index: 999;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .mobile-menu.active {
            right: 0;
        }

        .mobile-nav-links {
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .mobile-nav-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 1.3rem;
            font-weight: 500;
            transition: color var(--transition-speed) ease;
            display: block;
            padding: 0.5rem 0;
        }

        .mobile-nav-links a:hover {
            color: var(--accent-color);
        }

        .mobile-nav-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
        }

        .mobile-nav-buttons .btn {
            width: 100%;
            text-align: center;
        }

        .overlay {
            position: fixed;
            top: 80px;
            left: 0;
            width: 100%;
            height: calc(100vh - 80px);
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 998;
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-speed) ease;
        }

        .overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .nav-links {
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .nav-links, .nav-buttons {
                display: none;
            }

            .hamburger {
                display: block;
            }

            .navbar {
                padding: 1rem 5%;
                height: 70px;
            }

            .mobile-menu {
                top: 70px;
                height: calc(100vh - 70px);
            }

            .overlay {
                top: 70px;
                height: calc(100vh - 70px);
            }
        }

        @media (max-width: 480px) {
            .navbar {
                padding: 1rem 4%;
                height: 60px;
            }

            .logo-icon {
                font-size: 1.7rem;
            }

            .logo-text {
                font-size: 1.3rem;
            }

            .mobile-menu {
                top: 60px;
                height: calc(100vh - 60px);
                width: 100%;
                max-width: none;
            }

            .overlay {
                top: 60px;
                height: calc(100vh - 60px);
            }
        }

        /* Content placeholder for demo */
        .content {
            margin-top: 100px;
            padding: 2rem 5%;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        .content h1 {
            color: var(--text-dark);
            margin-bottom: 1rem;
        }

        .content p {
            color: var(--text-dark);
            line-height: 1.6;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <nav class="navbar">
            <div class="logo">
                <i class="fas fa-globe logo-icon"></i>
                <span class="logo-text">WebSite</span>
            </div>
            
            <ul class="nav-links">
                <li><a href="#">Home</a></li>
                <li><a href="#">Find geniuses</a></li>
                <li><a href="#">Find gigs</a></li>
                <li><a href="#">About us</a></li>
            </ul>
            
            <div class="nav-buttons">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-signup">Sign Up</button>
            </div>
            
            <button class="hamburger" id="hamburger">
                <i class="fas fa-bars"></i>
            </button>
        </nav>
    </header>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobile-menu">
        <ul class="mobile-nav-links">
            <li><a href="#">Home</a></li>
            <li><a href="#">Find geniuses</a></li>
            <li><a href="#">Find gigs</a></li>
            <li><a href="#">About us</a></li>
        </ul>
        
        <div class="mobile-nav-buttons">
            <button class="btn btn-login">Login</button>
            <button class="btn btn-signup">Sign Up</button>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Content Section (for demo) -->
    <div class="content">
        <h1>Welcome to Our Responsive Website</h1>
        <p>This is a modern responsive website with a beautiful header. Try resizing your browser window to see how the design adapts to different screen sizes.</p>
        <p>The header includes a hamburger menu for mobile devices, smooth animations, and a clean design that works well on all devices.</p>
    </div>

    <!-- JavaScript -->
    <script>
        // DOM Elements
        const hamburger = document.getElementById('hamburger');
        const mobileMenu = document.getElementById('mobile-menu');
        const overlay = document.getElementById('overlay');
        const header = document.querySelector('.header');
        
        // Toggle mobile menu
        hamburger.addEventListener('click', () => {
            mobileMenu.classList.toggle('active');
            overlay.classList.toggle('active');
            
            // Toggle hamburger icon
            const icon = hamburger.querySelector('i');
            if (mobileMenu.classList.contains('active')) {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            } else {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
        
        // Close mobile menu when clicking on overlay
        overlay.addEventListener('click', () => {
            mobileMenu.classList.remove('active');
            overlay.classList.remove('active');
            
            // Reset hamburger icon
            const icon = hamburger.querySelector('i');
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        });
        
        // Header scroll effect
        let lastScrollTop = 0;
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Add shadow and reduce size on scroll down
            if (scrollTop > 50) {
                header.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.1)';
                header.style.backgroundColor = '#3a56d4'; // Slightly darker shade
            } else {
                header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
                header.style.backgroundColor = 'var(--primary-color)';
            }
            
            // Hide header on scroll down, show on scroll up
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }
            
            lastScrollTop = scrollTop;
        });
        
        // Close mobile menu on window resize (if screen becomes larger)
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && mobileMenu.classList.contains('active')) {
                mobileMenu.classList.remove('active');
                overlay.classList.remove('active');
                
                // Reset hamburger icon
                const icon = hamburger.querySelector('i');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
    </script>
</body>
</html>