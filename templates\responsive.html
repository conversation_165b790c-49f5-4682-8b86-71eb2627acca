<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GigGenius | The Freelance Marketplace</title>
    <link rel="icon" type="image/x-icon" href="/static/img/logo.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
            --background-light: #f8f9fa;
            --transition-speed: 0.3s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-color: var(--background-light);
            overflow-x: hidden;
        }

        /* Header Styles */
        .header {
            background-color: white;
            color: var(--text-dark);
            position: fixed;
            width: 100%;
            top: 0;
            left: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all var(--transition-speed) ease;
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0rem 1rem;
            max-width: 1400px;
            margin: 0 auto;
            height: 5.5rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            color: var(--primary-pink);
            margin-right: 1rem;
            text-decoration: none;
        }

        .logo img {
            width: 2.5rem;
            height: 2.5rem;
            margin-right: 0.5rem;
            border-radius: 50%;
            object-fit: cover;
        }

        .logo h1 {
            font-size: 1.5rem;
            margin: 0;
            font-weight: bold;
            color: var(--primary-pink);
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            list-style: none;
            align-items: center;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1.1rem;
            font-weight: 500;
            transition: color var(--transition-speed) ease;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.6rem 1.5rem;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-speed) ease;
            border: none;
            font-size: 1rem;
        }

        .btn-login {
            background-color: transparent;
            color: var(--primary-blue);
            border: 2px solid var(--primary-blue);
        }

        .btn-login:hover {
            background-color: var(--primary-blue);
            color: var(--text-light);
        }

        .btn-signup {
            background-color: var(--primary-pink);
            color: var(--text-light);
            border: 2px solid var(--primary-pink);
        }

        .btn-signup:hover {
            background-color: #a91a6d;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(205, 32, 139, 0.3);
        }

        .hamburger {
            display: none;
            cursor: pointer;
            background: none;
            border: none;
            color: var(--primary-blue);
            font-size: 1.8rem;
            padding: 0.5rem;
            min-width: 44px;
            min-height: 44px;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: background-color var(--transition-speed) ease;
        }

        .hamburger:hover {
            background-color: rgba(0, 74, 173, 0.1);
        }

        .hamburger:active {
            background-color: rgba(0, 74, 173, 0.2);
        }

        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            top: 5.5rem;
            right: -100%;
            width: 80%;
            max-width: 400px;
            height: calc(100vh - 5.5rem);
            background-color: white;
            border-left: 1px solid #eee;
            transition: right var(--transition-speed) ease;
            z-index: 999;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .mobile-menu.active {
            right: 0;
        }

        .mobile-nav-links {
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            text-align: center;
        }

        .mobile-nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 1.3rem;
            font-weight: 500;
            transition: color var(--transition-speed) ease;
            display: block;
            padding: 0.5rem 0;
            text-align: center;
        }

        .mobile-nav-links a:hover {
            color: var(--primary-pink);
        }

        .mobile-nav-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
            align-items: center;
        }

        .mobile-nav-buttons .btn {
            width: 80%;
            max-width: 250px;
            text-align: center;
        }

        .overlay {
            position: fixed;
            top: 5.5rem;
            left: 0;
            width: 100%;
            height: calc(100vh - 5.5rem);
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 998;
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-speed) ease;
        }

        .overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Responsive Styles */

        /* Large screens */
        @media (min-width: 1400px) {
            .navbar {
                padding: 0rem 2rem;
            }
        }

        /* Tablet landscape */
        @media (max-width: 1024px) {
            .nav-links {
                gap: 1.2rem;
            }

            .nav-links a {
                font-size: 1rem;
                padding: 0.4rem 0.8rem;
            }
        }

        /* Tablet portrait */
        @media (max-width: 992px) {
            .nav-links {
                gap: 1rem;
            }

            .logo img {
                width: 2.2rem;
                height: 2.2rem;
            }

            .logo h1 {
                font-size: 1.4rem;
            }

            .btn {
                min-width: 100px;
                height: 45px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 768px) {
            .nav-links, .nav-buttons {
                display: none;
            }

            .hamburger {
                display: flex;
            }

            .navbar {
                padding: 1rem 5%;
                height: 4.5rem;
            }

            .logo img {
                width: 2rem;
                height: 2rem;
            }

            .logo h1 {
                font-size: 1.3rem;
            }

            .mobile-menu {
                top: 4.5rem;
                height: calc(100vh - 4.5rem);
            }

            .overlay {
                top: 4.5rem;
                height: calc(100vh - 4.5rem);
            }

            .content {
                margin-top: 5rem;
            }
        }

        /* Small mobile phones */
        @media (max-width: 480px) {
            .navbar {
                padding: 0.8rem 4%;
                height: 4rem;
            }

            .logo img {
                width: 1.8rem;
                height: 1.8rem;
            }

            .logo h1 {
                font-size: 1.1rem;
            }

            .mobile-menu {
                top: 4rem;
                height: calc(100vh - 4rem);
                width: 100%;
                max-width: none;
                padding: 1.5rem;
            }

            .overlay {
                top: 4rem;
                height: calc(100vh - 4rem);
            }

            .content {
                margin-top: 4.5rem;
                padding: 1.5rem 4%;
            }

            .content h1 {
                font-size: 1.8rem;
                line-height: 1.2;
            }

            .content p {
                font-size: 0.95rem;
                line-height: 1.5;
            }

            .mobile-nav-links a {
                font-size: 1.2rem;
                padding: 0.6rem 0;
            }

            .mobile-nav-buttons .btn {
                height: 45px;
                font-size: 1rem;
            }
        }

        /* Very small phones */
        @media (max-width: 360px) {
            .navbar {
                padding: 0.6rem 3%;
                height: 3.5rem;
            }

            .logo img {
                width: 1.5rem;
                height: 1.5rem;
            }

            .logo h1 {
                font-size: 1rem;
            }

            .mobile-menu {
                top: 3.5rem;
                height: calc(100vh - 3.5rem);
                padding: 1rem;
            }

            .overlay {
                top: 3.5rem;
                height: calc(100vh - 3.5rem);
            }

            .content {
                margin-top: 4rem;
                padding: 1rem 3%;
            }

            .content h1 {
                font-size: 1.5rem;
            }

            .content p {
                font-size: 0.9rem;
            }

            .mobile-nav-links a {
                font-size: 1.1rem;
            }

            .mobile-nav-buttons .btn {
                width: 90%;
                height: 42px;
                font-size: 0.95rem;
            }
        }

        /* Content placeholder for demo */
        .content {
            margin-top: 6rem;
            padding: 2rem 5%;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        .content h1 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }

        .content p {
            color: var(--text-gray);
            line-height: 1.6;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <nav class="navbar">
            <a href="#" class="logo">
                <img src="/static/img/logo.png" alt="GigGenius Logo">
                <h1>GigGenius</h1>
            </a>
            
            <ul class="nav-links">
                <li><a href="#">Home</a></li>
                <li><a href="#">Find geniuses</a></li>
                <li><a href="#">Find gigs</a></li>
                <li><a href="#">About us</a></li>
            </ul>
            
            <div class="nav-buttons">
                <button class="btn btn-login">Log In</button>
                <button class="btn btn-signup">Join</button>
            </div>
            
            <button class="hamburger" id="hamburger">
                <i class="fas fa-bars"></i>
            </button>
        </nav>
    </header>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobile-menu">
        <ul class="mobile-nav-links">
            <li><a href="#">Home</a></li>
            <li><a href="#">Find geniuses</a></li>
            <li><a href="#">Find gigs</a></li>
            <li><a href="#">About us</a></li>
        </ul>
        
        <div class="mobile-nav-buttons">
            <button class="btn btn-login">Log In</button>
            <button class="btn btn-signup">Join</button>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Content Section (for demo) -->
    <div class="content">
        <h1>Welcome to GigGenius</h1>
        <p>The Freelance Marketplace for GENIUSES looking for GIGS. Your True Ally to Success!</p>
        <p>A secured platform where GENIUSES can apply for ready to roll GIGS without the need to spend for connects or bids. Try resizing your browser window to see how the responsive design adapts to different screen sizes.</p>
    </div>

    <!-- JavaScript -->
    <script>
        // DOM Elements
        const hamburger = document.getElementById('hamburger');
        const mobileMenu = document.getElementById('mobile-menu');
        const overlay = document.getElementById('overlay');
        const header = document.querySelector('.header');
        
        // Toggle mobile menu
        hamburger.addEventListener('click', () => {
            mobileMenu.classList.toggle('active');
            overlay.classList.toggle('active');
            
            // Toggle hamburger icon
            const icon = hamburger.querySelector('i');
            if (mobileMenu.classList.contains('active')) {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            } else {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
        
        // Close mobile menu when clicking on overlay
        overlay.addEventListener('click', () => {
            mobileMenu.classList.remove('active');
            overlay.classList.remove('active');
            
            // Reset hamburger icon
            const icon = hamburger.querySelector('i');
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        });
        
        // Header scroll effect
        let lastScrollTop = 0;
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Add shadow and slight background change on scroll
            if (scrollTop > 50) {
                header.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.15)';
                header.style.backgroundColor = '#ffffff';
            } else {
                header.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                header.style.backgroundColor = 'white';
            }
            
            // Hide header on scroll down, show on scroll up
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }
            
            lastScrollTop = scrollTop;
        });
        
        // Close mobile menu on window resize (if screen becomes larger)
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && mobileMenu.classList.contains('active')) {
                mobileMenu.classList.remove('active');
                overlay.classList.remove('active');
                
                // Reset hamburger icon
                const icon = hamburger.querySelector('i');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
    </script>
</body>
</html>