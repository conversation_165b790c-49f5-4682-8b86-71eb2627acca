<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Details - GigGenius 2025</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/job-details-2025.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Validation error styles */
        .validation-error {
            border: 2px solid #F44336 !important;
            box-shadow: 0 0 5px rgba(244, 67, 54, 0.3) !important;
            animation: shake 0.5s ease-in-out;
            background-color: rgba(244, 67, 54, 0.05) !important;
        }

        .detail-section.validation-error {
            border: 2px solid #F44336;
            background-color: rgba(244, 67, 54, 0.05);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .scope-size-option.validation-error {
            border: 2px solid #F44336;
            background-color: rgba(244, 67, 54, 0.1);
            border-radius: 8px;
        }

        .budget-type-option.validation-error {
            border: 2px solid #F44336;
            background-color: rgba(244, 67, 54, 0.1);
            border-radius: 8px;
        }

        .fixed-price-option.validation-error {
            border: 2px solid #F44336;
            background-color: rgba(244, 67, 54, 0.1);
            border-radius: 8px;
        }

        #edit-skills-list.validation-error {
            border: 2px solid #F44336;
            background-color: rgba(244, 67, 54, 0.1);
            border-radius: 8px;
            min-height: 40px;
            padding: 10px;
        }

        .skills-list.validation-error {
            border: 2px solid #F44336;
            background-color: rgba(244, 67, 54, 0.1);
            border-radius: 8px;
            min-height: 40px;
            padding: 10px;
        }

        .scope-details.validation-error {
            border: 2px solid #F44336;
            background-color: rgba(244, 67, 54, 0.05);
            border-radius: 8px;
            padding: 15px;
        }

        .budget-details.validation-error {
            border: 2px solid #F44336;
            background-color: rgba(244, 67, 54, 0.05);
            border-radius: 8px;
            padding: 15px;
        }

        /* Validation message styles */
        .validation-message {
            color: #F44336;
            font-size: 14px;
            margin-top: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .validation-message i {
            font-size: 12px;
        }

        /* Required field indicator */
        .required-field::after {
            content: " *";
            color: #F44336;
            font-weight: bold;
        }

        /* Submit button disabled state */
        .btn-primary:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .btn-secondary:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .time-limit-input {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .time-limit-input .form-control {
            flex: 1;
            max-width: 120px;
        }

        .input-suffix {
            color: #666;
            font-size: 14px;
            white-space: nowrap;
        }

        /* Milestone breakdown styles */
        .milestone-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }

        .milestone-header {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .milestone-number {
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .milestone-fields {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            align-items: start;
        }

        /* Layout for milestone fields */
        .milestone-fields .form-group:nth-child(1) {
            /* Activity/Title - full width */
            grid-column: 1;
        }

        .milestone-fields .form-group:nth-child(2) {
            /* Description - full width */
            grid-column: 1;
        }

        /* Create a sub-grid for deadline and payment */
        .milestone-bottom-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            align-items: end;
        }

        .milestone-summary {
            margin-top: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            text-align: right;
        }

        @media (max-width: 768px) {
            .milestone-fields {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Enhanced Modal styles for better UX */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 2% auto 0;
            padding: 30px;
            border: none;
            border-radius: 15px;
            width: 90%;
            max-width: 700px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-height: 85vh;
            overflow-y: auto;
        }

        /* Special styling for budget modal - make it larger */
        #budgetModal .modal-content {
            max-width: 1100px;
            width: 95%;
            margin: 1% auto 0;
            max-height: 90vh;
            padding: 40px;
        }

        /* Enhanced milestone breakdown for better spacing */
        #budgetModal .milestone-item {
            margin-bottom: 20px;
            padding: 20px;
        }

        #budgetModal .milestone-fields {
            gap: 20px;
        }

        #budgetModal .form-group {
            margin-bottom: 20px;
        }

        #budgetModal .milestone-summary {
            margin-top: 25px;
            padding: 15px;
            font-size: 18px;
        }

        /* Project title styling */
        #project-main-title {
            font-weight: 600;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        #project-main-title:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* Milestone description textarea styling */
        .milestone-item textarea {
            resize: vertical;
            min-height: 70px;
            max-height: 150px;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            font-family: 'Poppins', sans-serif;
            font-size: 14px;
            line-height: 1.4;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            background-color: #fff;
            width: 100%;
            box-sizing: border-box;
        }

        .milestone-item textarea:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
            outline: none;
        }

        .milestone-item textarea::placeholder {
            color: #999;
            font-style: italic;
        }

        /* Required field indicator */
        .form-group label span[style*="color: #e74c3c"] {
            font-weight: bold;
        }

        /* Optional field indicator */
        .form-group label span[style*="color: #6c757d"] {
            font-size: 0.9em;
            font-style: italic;
        }



        /* Responsive adjustments for mobile */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                margin: 1% auto 0;
                padding: 20px;
            }

            #budgetModal .modal-content {
                width: 98%;
                margin: 0.5% auto 0;
                padding: 25px;
            }

            #budgetModal .milestone-fields {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .milestone-bottom-row {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <!-- Navigation Header -->
        <div class="navigation-header">
            <button type="button" class="back-button" onclick="goBackToPage2()">
                <i class="fas fa-arrow-left"></i>
                <span>Back</span>
            </button>

            <!-- Progress Indicator in Header -->
            <div class="header-progress-indicator">
                <div class="progress-step">
                    <div class="step-number">1</div>
                    <div class="step-label">Job Type</div>
                </div>
                <div class="step-connector"></div>
                <div class="progress-step">
                    <div class="step-number">2</div>
                    <div class="step-label">Category</div>
                </div>
                <div class="step-connector"></div>
                <div class="progress-step">
                    <div class="step-number active">3</div>
                    <div class="step-label active">Review & Post</div>
                </div>
            </div>
        </div>

        <div class="job-details-container">
            <h1 class="page-title">Create Your Job</h1>

            <!-- Title Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Job Title</h2>
                    <button class="edit-button" onclick="openEditModal('title')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <p id="job-title">{{ title }}</p>
                <div class="job-status draft">Draft</div>
            </div>

            <!-- Category Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Job Category</h2>
                    <button class="edit-button" onclick="openEditModal('category')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <div class="category-details">
                    <div class="category-meta-item">
                        <span class="meta-label"><i class="fas fa-folder"></i> Category</span>
                        <span class="meta-value" id="job-category">{{ job_category }}</span>
                    </div>
                    <div class="category-meta-item">
                        <span class="meta-label"><i class="fas fa-tag"></i> Specialty</span>
                        <span class="meta-value" id="job-specialty">{{ specialty if specialty and specialty != 'General' and specialty.strip() else 'None' }}</span>
                    </div>
                    <div class="category-meta-item">
                        <span class="meta-label"><i class="fas fa-briefcase"></i> Job Type</span>
                        <span class="meta-value" id="job-type">{{ job_type }}</span>
                    </div>
                </div>
            </div>

            <!-- Description Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Job Description</h2>
                    <button class="edit-button" onclick="openEditModal('description')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <div class="form-group">
                    <textarea id="job-description" class="form-control" rows="5" placeholder="Describe your job requirements and expectations..." data-session-value="{{ session.get('description', '') }}">{{ session.get('description', '') }}</textarea>
                </div>
            </div>

            <!-- Skills Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Required Skills</h2>
                    <button class="edit-button" onclick="openEditModal('skills')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <div id="skills-container">
                    <div class="skills-list" data-session-skills="{{ session.get('skills', '') }}">
                        <!-- Skills will be added here when the user selects them -->
                    </div>
                </div>
            </div>

            <!-- Scope Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Project Scope</h2>
                    <button class="edit-button" onclick="openEditModal('scope')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <div class="scope-details">
                    <div class="scope-description">
                        <h3>Description</h3>
                        <p id="job-scope-description">{{ session.get('project_description', '') }}</p>
                    </div>

                    <div class="scope-meta">
                        <div class="scope-meta-item">
                            <span class="meta-label"><i class="fas fa-chart-bar"></i> Project Size</span>
                            <span class="meta-value" id="job-scope-size">{{ session.get('project_size', '0') }}</span>
                        </div>

                        <div class="scope-meta-item">
                            <span class="meta-label"><i class="fas fa-calendar-alt"></i> Duration</span>
                            <span class="meta-value" id="job-scope-duration">{{ session.get('duration', '0') }}</span>
                        </div>

                        <div class="scope-meta-item">
                            <span class="meta-label"><i class="fas fa-user-graduate"></i> Experience Level</span>
                            <span class="meta-value" id="job-scope-experience">{{ session.get('experience_level', '0') }}</span>
                        </div>


                    </div>
                </div>
            </div>

            <!-- Budget Section -->
            <div class="detail-section">
                <div class="section-header">
                    <h2>Budget</h2>
                    <button class="edit-button" onclick="openEditModal('budget')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
                <div class="budget-details">
                    <p id="job-budget">{% if session.get('budget_type') == 'hourly' %}${{ session.get('budget_amount', '0') }} (Hourly Rate){% else %}${{ session.get('budget_amount', '0') }}{% if session.get('budget_amount') %} (Fixed Price){% endif %}{% endif %}</p>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="submit-buttons">
                {% if not session.get('edit_job_id') %}
                <button class="btn-secondary" onclick="submitJob('draft')">
                    <i class="fas fa-save"></i> Draft Post
                </button>
                {% endif %}
                <button class="btn-primary" onclick="submitJob('publish')">
                    <i class="fas fa-paper-plane"></i> {% if session.get('edit_job_id') %}Update Job{% else %}Post Job{% endif %}
                </button>
            </div>
        </div>
    </div>

    <!-- Edit Title Modal -->
    <div id="titleModal" class="modal">
        <div class="modal-content">
            <h2>Edit Job Title</h2>
            <div class="form-group">
                <label for="edit-title">Job Title</label>
                <input type="text" id="edit-title" class="form-control" value="{{ title }}">
            </div>
            <button class="btn-primary" onclick="saveEdit('title')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('titleModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div id="categoryModal" class="modal">
        <div class="modal-content">
            <h2>Edit Job Category</h2>

            <!-- Category Selection -->
            <div class="form-group">
                <label for="category-selection-type">Category Selection</label>
                <div class="selection-type-toggle">
                    <button type="button" class="selection-toggle active" onclick="toggleCategorySelectionType('predefined')">Choose from list</button>
                    <button type="button" class="selection-toggle" onclick="toggleCategorySelectionType('custom')">Enter custom</button>
                </div>
            </div>

            <!-- Predefined Category Selection -->
            <div id="predefined-category" class="form-group">
                <label for="edit-category">Job Category</label>
                <select id="edit-category" class="form-control" onchange="updateSpecialties()">
                    <option value="Graphic Design" {% if job_category == 'Graphic Design' %}selected{% endif %}>Graphic Design</option>
                    <option value="General Translation Services" {% if job_category == 'General Translation Services' %}selected{% endif %}>General Translation Services</option>
                    <option value="Social Media Marketing" {% if job_category == 'Social Media Marketing' %}selected{% endif %}>Social Media Marketing</option>
                    <option value="Web Development" {% if job_category == 'Web Development' %}selected{% endif %}>Web Development</option>
                    <option value="Content Writing" {% if job_category == 'Content Writing' %}selected{% endif %}>Content Writing</option>
                    <option value="AI & Machine Learning" {% if job_category == 'AI & Machine Learning' %}selected{% endif %}>AI & Machine Learning</option>
                    <option value="Virtual Reality Design" {% if job_category == 'Virtual Reality Design' %}selected{% endif %}>Virtual Reality Design</option>
                    <option value="Blockchain Development" {% if job_category == 'Blockchain Development' %}selected{% endif %}>Blockchain Development</option>
                </select>
            </div>

            <!-- Custom Category Input -->
            <div id="custom-category" class="form-group" style="display: none;">
                <label for="custom-category-input">Custom Job Category</label>
                <input type="text" id="custom-category-input" class="form-control" placeholder="Enter your own category">
            </div>

            <!-- Specialty Selection -->
            <div class="form-group">
                <label for="specialty-selection-type">Specialty Selection</label>
                <div class="selection-type-toggle">
                    <button type="button" class="selection-toggle active" onclick="toggleSpecialtySelectionType('predefined')">Choose from list</button>
                    <button type="button" class="selection-toggle" onclick="toggleSpecialtySelectionType('custom')">Enter custom</button>
                </div>
            </div>

            <!-- Predefined Specialty Selection -->
            <div id="predefined-specialty" class="form-group">
                <label for="edit-specialty">Specialty</label>
                <select id="edit-specialty" class="form-control">
                    <!-- Graphic Design Specialties -->
                    <optgroup label="Graphic Design" class="specialty-group" data-category="Graphic Design">
                        <option value="Logo Design">Logo Design</option>
                        <option value="Brand Identity Design">Brand Identity Design</option>
                        <option value="Illustration">Illustration</option>
                        <option value="UI/UX Design">UI/UX Design</option>
                        <option value="Packaging Design">Packaging Design</option>
                        <option value="Print Design">Print Design</option>
                    </optgroup>

                    <!-- Translation Specialties -->
                    <optgroup label="Translation" class="specialty-group" data-category="General Translation Services">
                        <option value="Document Translation">Document Translation</option>
                        <option value="Website Localization">Website Localization</option>
                        <option value="Technical Translation">Technical Translation</option>
                        <option value="Legal Translation">Legal Translation</option>
                        <option value="Medical Translation">Medical Translation</option>
                    </optgroup>

                    <!-- Marketing Specialties -->
                    <optgroup label="Marketing" class="specialty-group" data-category="Social Media Marketing">
                        <option value="Content Marketing">Content Marketing</option>
                        <option value="Social Media Management">Social Media Management</option>
                        <option value="Email Marketing">Email Marketing</option>
                        <option value="SEO">SEO</option>
                        <option value="PPC Advertising">PPC Advertising</option>
                    </optgroup>

                    <!-- Web Development Specialties -->
                    <optgroup label="Web Development" class="specialty-group" data-category="Web Development">
                        <option value="Frontend Development">Frontend Development</option>
                        <option value="Backend Development">Backend Development</option>
                        <option value="Full Stack Development">Full Stack Development</option>
                        <option value="E-commerce Development">E-commerce Development</option>
                        <option value="WordPress Development">WordPress Development</option>
                    </optgroup>

                    <!-- Content Writing Specialties -->
                    <optgroup label="Content Writing" class="specialty-group" data-category="Content Writing">
                        <option value="Blog Writing">Blog Writing</option>
                        <option value="Copywriting">Copywriting</option>
                        <option value="Technical Writing">Technical Writing</option>
                        <option value="Creative Writing">Creative Writing</option>
                        <option value="SEO Writing">SEO Writing</option>
                    </optgroup>

                    <!-- AI & ML Specialties -->
                    <optgroup label="AI & Machine Learning" class="specialty-group" data-category="AI & Machine Learning">
                        <option value="Natural Language Processing">Natural Language Processing</option>
                        <option value="Computer Vision">Computer Vision</option>
                        <option value="Predictive Analytics">Predictive Analytics</option>
                        <option value="Chatbot Development">Chatbot Development</option>
                        <option value="Deep Learning">Deep Learning</option>
                    </optgroup>

                    <!-- VR Design Specialties -->
                    <optgroup label="VR Design" class="specialty-group" data-category="Virtual Reality Design">
                        <option value="3D Modeling">3D Modeling</option>
                        <option value="VR Environment Design">VR Environment Design</option>
                        <option value="AR Experience Design">AR Experience Design</option>
                        <option value="Interactive VR">Interactive VR</option>
                    </optgroup>

                    <!-- Blockchain Specialties -->
                    <optgroup label="Blockchain" class="specialty-group" data-category="Blockchain Development">
                        <option value="Smart Contract Development">Smart Contract Development</option>
                        <option value="DApp Development">DApp Development</option>
                        <option value="Cryptocurrency">Cryptocurrency</option>
                        <option value="NFT Development">NFT Development</option>
                    </optgroup>
                </select>
            </div>crea

            <!-- Custom Specialty Input -->
            <div id="custom-specialty" class="form-group" style="display: none;">
                <label for="custom-specialty-input">Custom Specialty</label>
                <input type="text" id="custom-specialty-input" class="form-control" placeholder="Enter your own specialty">
            </div>

            <button class="btn-primary" onclick="saveEdit('category')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('categoryModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>

    <!-- Edit Description Modal -->
    <div id="descriptionModal" class="modal">
        <div class="modal-content">
            <h2>Edit Job Description</h2>
            <div class="form-group">
                <label for="edit-description">Job Description</label>
                <textarea id="edit-description" class="form-control" rows="8" placeholder="Provide detailed information about the job requirements, responsibilities, and qualifications..."></textarea>
            </div>
            <button class="btn-primary" onclick="saveEdit('description')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('descriptionModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>

    <!-- Edit Skills Modal -->
    <div id="skillsModal" class="modal">
        <div class="modal-content">
            <h2>Edit Required Skills</h2>
            <div class="form-group">
                <label for="skills-input">Add Skills</label>
                <div class="skills-input-container">
                    <input type="text" id="skills-input" class="form-control" placeholder="Type a skill and press Enter">
                    <button type="button" id="add-skill-btn" class="add-skill-btn"><i class="fas fa-plus"></i></button>
                </div>
                <p class="input-help">Type a skill and press Enter or click the + button to add it</p>
            </div>
            <div class="form-group">
                <label>Selected Skills</label>
                <div id="edit-skills-list" class="skills-edit-list">
                    <!-- Selected skills will appear here -->
                </div>
                <p class="input-help">Click on a skill to remove it</p>
            </div>
            <div class="form-group">
                <label>Suggested Skills</label>
                <div class="skills-suggestions">
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('JavaScript')">JavaScript</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('Python')">Python</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('React')">React</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('Node.js')">Node.js</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('UI/UX Design')">UI/UX Design</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('Machine Learning')">Machine Learning</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('Data Analysis')">Data Analysis</span>
                    <span class="skill-suggestion" onclick="addSkillFromSuggestion('AWS')">AWS</span>
                </div>
            </div>
            <button class="btn-primary" onclick="saveEdit('skills')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('skillsModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>

    <!-- Edit Scope Modal -->
    <div id="scopeModal" class="modal">
        <div class="modal-content">
            <h2>Edit Project Scope</h2>
            <div class="form-group">
                <label for="edit-scope">Project Description</label>
                <textarea id="edit-scope" class="form-control" rows="4" placeholder="Define the boundaries and deliverables of your project...">{{ session.get('project_description', '') }}</textarea>
            </div>

            <div class="form-group">
                <label>Project Size</label>
                <div class="scope-size-options">
                    <label class="scope-size-option">
                        <input type="radio" name="project-size" value="small" id="size-small" {% if session.get('project_size') and session.get('project_size', '').lower() == 'small' %}checked{% endif %}>
                        <span class="option-label">
                            <i class="fas fa-chart-pie"></i>
                            <span>Small</span>
                            <small>Simple task, quick turnaround</small>
                        </span>
                    </label>
                    <label class="scope-size-option">
                        <input type="radio" name="project-size" value="medium" id="size-medium" {% if session.get('project_size') and session.get('project_size', '').lower() == 'medium' %}checked{% endif %}>
                        <span class="option-label">
                            <i class="fas fa-chart-bar"></i>
                            <span>Medium</span>
                            <small>Standard complexity, moderate timeline</small>
                        </span>
                    </label>
                    <label class="scope-size-option">
                        <input type="radio" name="project-size" value="large" id="size-large" {% if session.get('project_size') and session.get('project_size', '').lower() == 'large' %}checked{% endif %}>
                        <span class="option-label">
                            <i class="fas fa-chart-line"></i>
                            <span>Large</span>
                            <small>Complex project, extended timeline</small>
                        </span>
                    </label>
                </div>
            </div>

            <div class="scope-flex-row">
                <div class="form-group flex-1">
                    <label>Duration</label>
                    <div class="scope-duration">
                        <div class="duration-input">
                            <input type="number" id="scope-duration-value" class="form-control" min="0" value="{% if session.get('duration') and ' ' in session.get('duration') %}{{ session.get('duration').split(' ')[0] }}{% else %}0{% endif %}" onchange="updateDurationOptions()" oninput="updateDurationOptions()">
                            <select id="scope-duration-unit" class="form-control">
                                <option value="days" {% if not session.get('duration') or 'days' in session.get('duration', '').lower() %}selected{% endif %}>Days</option>
                                <option value="weeks" {% if session.get('duration') and 'weeks' in session.get('duration').lower() %}selected{% endif %}>Weeks</option>
                                <option value="months" {% if session.get('duration') and 'months' in session.get('duration').lower() %}selected{% endif %}>Months</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group flex-1">
                    <label>Experience Level</label>
                    <select id="scope-experience" class="form-control">
                        <option value="">Select experience level</option>
                        <option value="entry" {% if session.get('experience_level') and session.get('experience_level').lower() == 'entry' %}selected{% endif %}>Entry Level</option>
                        <option value="intermediate" {% if session.get('experience_level') and session.get('experience_level').lower() == 'intermediate' %}selected{% endif %}>Intermediate</option>
                        <option value="expert" {% if session.get('experience_level') and session.get('experience_level').lower() == 'expert' %}selected{% endif %}>Expert</option>
                    </select>
                </div>
            </div>



            <button class="btn-primary" onclick="saveEdit('scope')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('scopeModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>
crea
    <!-- Edit Budget Modal -->
    <div id="budgetModal" class="modal">
        <div class="modal-content">
            <h2>Edit Budget</h2>
            <div class="form-group">
                <label>Payment Type</label>
                <div class="budget-type-options">
                    <label class="budget-type-option">
                        <input type="radio" name="budget-type" value="hourly" id="budget-hourly" {% if session.get('budget_type') == 'hourly' or session.get('budget_type') == 'time_tracking' %}checked{% endif %}>
                        <span class="option-label">
                            <i class="fas fa-clock"></i>
                            <span>Hourly Pay</span>
                            <small>Time tracking based payment</small>
                        </span>
                    </label>
                    <label class="budget-type-option">
                        <input type="radio" name="budget-type" value="fixed" id="budget-fixed" {% if session.get('budget_type') == 'fixed' or session.get('budget_type') == 'milestone' or session.get('budget_type') == 'one_time_pay' or not session.get('budget_type') %}checked{% endif %}>
                        <span class="option-label">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>Fixed Price</span>
                            <small>One time pay or milestone based</small>
                        </span>
                    </label>
                </div>
            </div>
            <!-- Hourly Pay Fields (Time Tracking) -->
            <div id="hourly-fields" style="display: none;">
                <div class="form-group">
                    <label for="budget-hourly-rate">Hourly Rate ($)</label>
                    <input type="number" id="budget-hourly-rate" class="form-control" value="{{ session.get('budget_amount', '0') }}" min="0" step="0.01">
                </div>

                <div class="form-group">
                    <label for="weekly-time-limit">Weekly Time Limit</label>
                    <div class="time-limit-input">
                        <input type="number" id="weekly-time-limit" class="form-control" min="1" max="168" placeholder="e.g., 40">
                        <span class="input-suffix">hours/week</span>
                    </div>
                    <small class="form-text text-muted">Set the maximum number of hours that can be worked per week (1-168 hours)</small>
                </div>

                <p class="budget-help-text">Set your hourly rate and weekly time limit. Payment will be based on time tracking.</p>
            </div>

            <!-- Fixed Price Fields -->
            <div id="fixed-fields">
                <div class="form-group">
                    <label>Fixed Price Type</label>
                    <div class="fixed-price-options">
                        <label class="fixed-price-option">
                            <input type="radio" name="fixed-price-type" value="one_time" id="fixed-one-time" checked>
                            <span class="option-label">
                                <i class="fas fa-money-bill"></i>
                                <span>One time Pay</span>
                                <small>Single payment for entire project</small>
                            </span>
                        </label>
                        <label class="fixed-price-option">
                            <input type="radio" name="fixed-price-type" value="milestone" id="fixed-milestone">
                            <span class="option-label">
                                <i class="fas fa-flag-checkered"></i>
                                <span>Milestone</span>
                                <small>Pay upon completion of milestones</small>
                            </span>
                        </label>
                    </div>
                </div>

                <!-- One Time Pay Sub-fields -->
                <div id="one-time-sub-fields">
                    <div class="form-group">
                        <label for="budget-one-time-amount">Project Amount ($)</label>
                        <input type="number" id="budget-one-time-amount" class="form-control" value="{{ session.get('budget_amount', '0') }}" min="0" step="0.01">
                    </div>
                    <p class="budget-help-text">Single payment for the entire project upon completion.</p>
                </div>

                <!-- Milestone Sub-fields -->
                <div id="milestone-sub-fields" style="display: none;">
                    <!-- Main Project Title -->
                    <div class="form-group">
                        <label for="project-main-title">Project Title <span style="color: #e74c3c;">*</span></label>
                        <input type="text" id="project-main-title" class="form-control" placeholder="e.g., Web Development Project, Mobile App Development, etc." required>
                        <small class="form-text text-muted">Enter the main title/name for your project</small>
                    </div>

                    <div class="form-group">
                        <label for="milestone-count">Number of Milestones</label>
                        <select id="milestone-count" class="form-control" onchange="updateMilestoneBreakdown()">
                            <option value="2">2 Milestones</option>
                            <option value="3" selected>3 Milestones</option>
                            <option value="4">4 Milestones</option>
                            <option value="5">5 Milestones</option>
                            <option value="6">6 Milestones</option>
                            <option value="7">7 Milestones</option>
                            <option value="8">8 Milestones</option>
                            <option value="9">9 Milestones</option>
                            <option value="10">10 Milestones</option>
                            <option value="custom">Custom (More than 10)</option>
                        </select>
                    </div>
                    <div id="custom-milestone-input" class="form-group" style="display: none;">
                        <label for="custom-milestone-count">Custom Number of Milestones</label>
                        <input type="number" id="custom-milestone-count" class="form-control" min="11" max="50" placeholder="Enter number (11-50)" onchange="updateMilestoneBreakdown()">
                        <small class="form-text text-muted">Enter a number between 11 and 50 milestones</small>
                    </div>

                    <!-- Milestone Breakdown Section -->
                    <div id="milestone-breakdown" class="form-group" style="display: none;">
                        <label>Milestone Breakdown</label>
                        <div id="milestone-details-container">
                            <!-- Dynamic milestone details will be generated here -->
                        </div>
                        <div class="milestone-summary">
                            <strong>Total Project Budget: $<span id="milestone-total-display">0</span></strong>
                        </div>
                    </div>

                    <!-- Hidden field to store the calculated total -->
                    <input type="hidden" id="budget-milestone-amount" value="0">

                    <p class="budget-help-text">Define each milestone with title, deadline, and payment amount. Total budget is calculated automatically.</p>
                </div>
            </div>
            <button class="btn-primary" onclick="saveEdit('budget')"><i class="fas fa-check"></i> Save Changes</button>
            <button onclick="closeModal('budgetModal')"><i class="fas fa-times"></i> Cancel</button>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/page3.js') }}"></script>

    <script>
        // Initialize progress indicator animation on page load
        document.addEventListener('DOMContentLoaded', function() {
            animateProgressSteps();
            // Initialize duration options
            updateDurationOptions();
        });

        // Animate progress steps
        function animateProgressSteps() {
            const steps = document.querySelectorAll('.step-number');
            steps.forEach((step, index) => {
                setTimeout(() => {
                    step.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        step.style.transform = step.classList.contains('active') ? 'scale(1.1)' : 'scale(1)';
                    }, 300);
                }, index * 200);
            });
        }
        // Back button functionality
        function goBackToPage2() {
            // Check if user has made any changes to the form
            const hasChanges = checkForUnsavedChanges();

            // Determine where to go back based on how user got here
            let backUrl = '/page2'; // default for new job creation

            // Check URL parameters to see if we came from editing
            const urlParams = new URLSearchParams(window.location.search);
            const editJobId = urlParams.get('edit_job_id');
            const draftJobId = urlParams.get('draft_job_id');

            // Check if we're in edit mode (either editing existing job or draft)
            const isDraftEdit = {{ 'true' if is_draft_edit else 'false' }};
            const hasEditJobIdInSession = {{ 'true' if session.get('edit_job_id') else 'false' }};
            const isEditMode = editJobId || draftJobId || isDraftEdit || hasEditJobIdInSession;

            // Debug logging
            console.log('Back button clicked - Debug info:');
            console.log('editJobId:', editJobId);
            console.log('draftJobId:', draftJobId);
            console.log('isDraftEdit:', isDraftEdit);
            console.log('hasEditJobIdInSession:', hasEditJobIdInSession);
            console.log('isEditMode:', isEditMode);
            console.log('document.referrer:', document.referrer);

            if (isEditMode) {
                // If we're editing, we should go back to allgigpost.html
                // This covers all editing scenarios:
                // 1. Direct edit from allgigpost -> page1 -> page3
                // 2. Draft edit from allgigpost -> page1 -> page3
                // 3. Direct draft edit from page1 -> page3
                backUrl = '/allgigpost';
                console.log('Edit mode detected - setting backUrl to:', backUrl);
            } else {
                // For new job creation, check the referrer
                if (document.referrer && document.referrer.includes('/page2')) {
                    backUrl = '/page2';
                } else if (document.referrer && document.referrer.includes('/page1')) {
                    backUrl = '/page2'; // Standard flow: page1 -> page2 -> page3
                } else {
                    backUrl = '/page2'; // Default for new jobs
                }
                console.log('New job mode detected - setting backUrl to:', backUrl);
            }

            console.log('Final backUrl:', backUrl);
            console.log('hasChanges:', hasChanges);

            if (hasChanges) {
                if (confirm('Are you sure you want to go back? Any unsaved changes will be lost.')) {
                    console.log('User confirmed - navigating to:', backUrl);
                    window.location.href = backUrl;
                } else {
                    console.log('User cancelled navigation');
                }
            } else {
                console.log('No changes detected - navigating to:', backUrl);
                window.location.href = backUrl;
            }
        }

        // Function to check for unsaved changes
        function checkForUnsavedChanges() {
            // Check if description has content
            const description = document.getElementById('job-description').value.trim();

            // Check if any skills have been added
            const skillsContainer = document.querySelector('.skills-list');
            const hasSkills = skillsContainer && skillsContainer.children.length > 0;

            // Return true if there are any changes
            return description.length > 0 || hasSkills;
        }

        // Update duration dropdown options based on entered value
        function updateDurationOptions() {
            const durationValue = parseInt(document.getElementById('scope-duration-value').value);
            const durationUnit = document.getElementById('scope-duration-unit');
            const currentValue = durationUnit.value;

            // Clear existing options
            durationUnit.innerHTML = '';

            // Determine if we should use singular or plural forms
            const isSingular = durationValue === 1;

            // Add options with appropriate singular/plural forms
            const daysOption = document.createElement('option');
            daysOption.value = 'days';
            daysOption.textContent = isSingular ? 'Day' : 'Days';
            durationUnit.appendChild(daysOption);

            const weeksOption = document.createElement('option');
            weeksOption.value = 'weeks';
            weeksOption.textContent = isSingular ? 'Week' : 'Weeks';
            durationUnit.appendChild(weeksOption);

            const monthsOption = document.createElement('option');
            monthsOption.value = 'months';
            monthsOption.textContent = isSingular ? 'Month' : 'Months';
            durationUnit.appendChild(monthsOption);

            // Restore the previously selected value or set default
            if (currentValue) {
                durationUnit.value = currentValue;
            } else {
                // Auto-suggest unit based on typical project durations
                if (durationValue > 0) {
                    if (durationValue <= 14) {
                        durationUnit.value = 'days';
                    } else if (durationValue <= 12) {
                        durationUnit.value = 'weeks';
                    } else {
                        durationUnit.value = 'months';
                    }
                } else {
                    durationUnit.value = 'days'; // Default
                }
            }
        }




    </script>
</body>

</html>