<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --primary-light-blue: rgba(0, 74, 173, 0.1);
            --primary-light-pink: rgba(205, 32, 139, 0.1);
            --neutral-100: #f8fafc;
            --neutral-200: #e2e8f0;
            --neutral-300: #cbd5e1;
            --neutral-400: #94a3b8;
            --neutral-500: #64748b;
            --neutral-600: #475569;
            --neutral-700: #334155;
            --neutral-800: #1e293b;
            --neutral-900: #0f172a;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--neutral-800);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header Navigation */
        .header {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            box-shadow: 0 2px 20px rgba(0, 74, 173, 0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            color: var(--primary-pink);
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            color: var(--primary-pink);
        }

        .logo:hover, .logo:active {
            color: var(--primary-blue);
        }

        .logo:hover h1 {
            color: var(--primary-blue);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: var(--neutral-700);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 8px;
        }

        .nav-link:hover {
            color: var(--primary-blue);
            background: var(--primary-light-blue);
        }

        .back-btn {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 74, 173, 0.3);
            color: white;
            text-decoration: none;
        }

        /* Main Container */
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        /* Profile Header */
        .profile-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 40px rgba(0, 74, 173, 0.1);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .profile-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
        }

        .profile-header-content {
            display: flex;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .profile-avatar {
            position: relative;
        }

        .profile-avatar img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid white;
            box-shadow: 0 8px 25px rgba(0, 74, 173, 0.2);
        }

        .profile-avatar .edit-avatar {
            position: absolute;
            bottom: 5px;
            right: 5px;
            background: var(--primary-blue);
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid white;
        }

        .profile-avatar .edit-avatar:hover {
            background: var(--primary-pink);
            transform: scale(1.1);
        }

        .profile-info {
            flex: 1;
        }

        .profile-name {
            font-size: 2rem;
            font-weight: 700;
            color: var(--neutral-900);
            margin-bottom: 0.5rem;
        }

        .profile-title {
            font-size: 1.2rem;
            color: var(--primary-blue);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .profile-company {
            font-size: 1rem;
            color: var(--neutral-600);
            margin-bottom: 1rem;
        }

        .profile-stats {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-blue);
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--neutral-600);
        }

        /* Profile Actions */
        .profile-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 74, 173, 0.3);
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: white;
            color: var(--neutral-700);
            border: 2px solid var(--neutral-300);
        }

        .btn-secondary:hover {
            background: var(--neutral-100);
            border-color: var(--primary-blue);
            color: var(--primary-blue);
            text-decoration: none;
        }

        /* Profile Content Grid */
        .profile-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        /* Profile Cards */
        .profile-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 74, 173, 0.08);
            border: 1px solid rgba(0, 74, 173, 0.05);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--neutral-100);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--neutral-900);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-title i {
            color: var(--primary-blue);
        }

        .edit-btn {
            color: var(--primary-blue);
            background: var(--primary-light-blue);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .edit-btn:hover {
            background: var(--primary-blue);
            color: white;
            text-decoration: none;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--neutral-700);
            margin-bottom: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--neutral-200);
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        /* Info Display */
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--neutral-100);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: var(--neutral-700);
        }

        .info-value {
            color: var(--neutral-600);
            text-align: right;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .profile-content {
                grid-template-columns: 1fr;
            }

            .profile-header-content {
                flex-direction: column;
                text-align: center;
            }

            .profile-stats {
                justify-content: center;
            }

            .nav-container {
                padding: 0 1rem;
            }

            .container {
                padding: 0 1rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .nav-link {
                padding: 0.5rem;
            }

            .logo img {
                width: 2.8rem;
                height: 2.8rem;
            }

            .logo h1 {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 480px) {
            .logo img {
                width: 2.5rem;
                height: 2.5rem;
            }

            .logo h1 {
                font-size: 1rem;
                margin-left: 0.2rem;
                margin-right: 0.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <div class="nav-container">
            <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                <div class="logo">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </div>
            </a>
            <nav class="nav-links">
                <a href="/client_page" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    Back to Dashboard
                </a>
            </nav>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-header-content">
                <div class="profile-avatar">
                    <img src="/api/client-photo/{{ client_data.id }}"
                         alt="Profile Picture" id="profileImage"
                         onerror="this.src='/static/img/default-avatar.png'">
                    <div class="edit-avatar" onclick="document.getElementById('profilePhotoInput').click()">
                        <i class="fas fa-camera"></i>
                    </div>
                    <input type="file" id="profilePhotoInput" style="display: none;" accept="image/*">
                </div>
                <div class="profile-info">
                    <h1 class="profile-name">{{ client_data.first_name }} {{ client_data.last_name }}</h1>
                    <div class="profile-title">{{ client_data.position or 'Business Professional' }}</div>
                    <div class="profile-company">
                        <i class="fas fa-building"></i>
                        {{ client_data.business_name or 'Company Name' }}
                    </div>
                    <div class="profile-stats">
                        <div class="stat-item">
                            <span class="stat-number" id="totalJobs">{{ job_stats.total_jobs or 0 }}</span>
                            <span class="stat-label">Jobs Posted</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="activeJobs">{{ job_stats.active_jobs or 0 }}</span>
                            <span class="stat-label">Active Jobs</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="totalHires">{{ job_stats.total_hires or 0 }}</span>
                            <span class="stat-label">Total Hires</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="profile-actions">
                <button class="btn btn-primary" onclick="enableEditMode()">
                    <i class="fas fa-edit"></i>
                    Edit Profile
                </button>
                <a href="/client_page" class="btn btn-secondary">
                    <i class="fas fa-briefcase"></i>
                    View Jobs
                </a>
            </div>
        </div>

        <!-- Profile Content Grid -->
        <div class="profile-content">
            <!-- Personal Information -->
            <div class="profile-card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-user"></i>
                        Personal Information
                    </h2>
                    <a href="#" class="edit-btn" onclick="enableEditMode()">
                        <i class="fas fa-edit"></i>
                        Edit
                    </a>
                </div>
                <div class="card-content" id="personalInfo">
                    <div class="info-item">
                        <span class="info-label">First Name</span>
                        <span class="info-value">{{ client_data.first_name or 'Not provided' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Last Name</span>
                        <span class="info-value">{{ client_data.last_name or 'Not provided' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Email</span>
                        <span class="info-value">{{ client_data.work_email or 'Not provided' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Phone</span>
                        <span class="info-value">{{ client_data.mobile or 'Not provided' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Country</span>
                        <span class="info-value">{{ client_data.country or 'Not provided' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Birthday</span>
                        <span class="info-value">{{ client_data.birthday or 'Not provided' }}</span>
                    </div>
                </div>
            </div>

            <!-- Business Information -->
            <div class="profile-card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-building"></i>
                        Business Information
                    </h2>
                    <a href="#" class="edit-btn" onclick="enableEditMode()">
                        <i class="fas fa-edit"></i>
                        Edit
                    </a>
                </div>
                <div class="card-content" id="businessInfo">
                    <div class="info-item">
                        <span class="info-label">Business Name</span>
                        <span class="info-value">{{ client_data.business_name or 'Not provided' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Industry</span>
                        <span class="info-value">{{ client_data.industry or 'Not provided' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Employee Count</span>
                        <span class="info-value">{{ client_data.employee_count or 'Not provided' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Business Address</span>
                        <span class="info-value">{{ client_data.business_address or 'Not provided' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Business Email</span>
                        <span class="info-value">{{ client_data.business_email or 'Not provided' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Website</span>
                        <span class="info-value">
                            {% if client_data.business_website %}
                                <a href="{{ client_data.business_website }}" target="_blank" style="color: var(--primary-blue);">
                                    {{ client_data.business_website }}
                                </a>
                            {% else %}
                                Not provided
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- About Section -->
        <div class="profile-card" style="grid-column: 1 / -1; margin-top: 2rem;">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-info-circle"></i>
                    About Me
                </h2>
                <a href="#" class="edit-btn" onclick="enableEditMode()">
                    <i class="fas fa-edit"></i>
                    Edit
                </a>
            </div>
            <div class="card-content" id="aboutInfo">
                <div class="info-item" style="flex-direction: column; align-items: flex-start;">
                    <span class="info-label" style="margin-bottom: 0.5rem;">Introduction</span>
                    <span class="info-value" style="text-align: left; line-height: 1.6;">
                        {{ client_data.introduction or 'Tell us about yourself and your business...' }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Profile Modal -->
    <div id="editModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 2000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 20px; padding: 2rem; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h2 style="color: var(--neutral-900); font-weight: 600;">Edit Profile</h2>
                <button onclick="closeEditMode()" style="background: none; border: none; font-size: 1.5rem; color: var(--neutral-500); cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="profileForm">
                <div class="form-group">
                    <label class="form-label">First Name</label>
                    <input type="text" class="form-input" id="editFirstName" value="{{ client_data.first_name or '' }}">
                </div>

                <div class="form-group">
                    <label class="form-label">Last Name</label>
                    <input type="text" class="form-input" id="editLastName" value="{{ client_data.last_name or '' }}">
                </div>

                <div class="form-group">
                    <label class="form-label">Position</label>
                    <input type="text" class="form-input" id="editPosition" value="{{ client_data.position or '' }}">
                </div>

                <div class="form-group">
                    <label class="form-label">Phone</label>
                    <input type="text" class="form-input" id="editMobile" value="{{ client_data.mobile or '' }}">
                </div>

                <div class="form-group">
                    <label class="form-label">Country</label>
                    <input type="text" class="form-input" id="editCountry" value="{{ client_data.country or '' }}">
                </div>

                <div class="form-group">
                    <label class="form-label">Business Name</label>
                    <input type="text" class="form-input" id="editBusinessName" value="{{ client_data.business_name or '' }}">
                </div>

                <div class="form-group">
                    <label class="form-label">Industry</label>
                    <input type="text" class="form-input" id="editIndustry" value="{{ client_data.industry or '' }}">
                </div>

                <div class="form-group">
                    <label class="form-label">Business Website <span style="color: #6b7280; font-weight: normal;">(Optional)</span></label>
                    <input type="text" class="form-input" id="editWebsite" value="{{ client_data.business_website or '' }}"
                           placeholder="https://example.com (optional)">
                </div>

                <div class="form-group">
                    <label class="form-label">Introduction</label>
                    <textarea class="form-input form-textarea" id="editIntroduction">{{ client_data.introduction or '' }}</textarea>
                </div>

                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeEditMode()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // URL validation function (only validates if URL is provided)
        function isValidUrl(string) {
            if (!string || string.trim() === '' || string.trim().toLowerCase() === 'na') {
                return true; // Empty, null, or "NA" is valid (optional field)
            }

            try {
                // Add protocol if missing
                let url = string.trim();
                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                    url = 'https://' + url;
                }

                new URL(url);
                return true;
            } catch (_) {
                return false;
            }
        }

        // Enable edit mode
        function enableEditMode() {
            document.getElementById('editModal').style.display = 'block';

            // Clean up "NA" values in website field when opening edit modal
            const websiteField = document.getElementById('editWebsite');
            if (websiteField && (websiteField.value.toLowerCase() === 'na' || websiteField.value.toLowerCase() === 'n/a')) {
                websiteField.value = '';
            }
        }

        // Close edit mode
        function closeEditMode() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Handle profile form submission
        document.getElementById('profileForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Get website value and validate if provided
            let websiteValue = document.getElementById('editWebsite').value.trim();

            // Convert "NA" or similar values to empty string
            if (websiteValue.toLowerCase() === 'na' || websiteValue.toLowerCase() === 'n/a' || websiteValue === '') {
                websiteValue = '';
            }

            // Basic URL validation only if website is provided and not empty
            if (websiteValue && !isValidUrl(websiteValue)) {
                showNotification('Please enter a valid website URL (e.g., https://example.com)', 'error');
                return;
            }

            // Add protocol if missing (for display purposes) - only for non-empty values
            if (websiteValue && !websiteValue.startsWith('http://') && !websiteValue.startsWith('https://')) {
                websiteValue = 'https://' + websiteValue;
            }

            const formData = {
                first_name: document.getElementById('editFirstName').value,
                last_name: document.getElementById('editLastName').value,
                position: document.getElementById('editPosition').value,
                mobile: document.getElementById('editMobile').value,
                country: document.getElementById('editCountry').value,
                business_name: document.getElementById('editBusinessName').value,
                industry: document.getElementById('editIndustry').value,
                business_website: websiteValue, // Use processed value
                introduction: document.getElementById('editIntroduction').value
            };

            // Show loading state
            const submitBtn = document.querySelector('#profileForm button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
            submitBtn.disabled = true;

            // Send update request
            fetch('/api/update-client-profile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the display
                    updateProfileDisplay(formData);
                    closeEditMode();
                    showNotification('Profile updated successfully!', 'success');
                } else {
                    showNotification('Error updating profile: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error updating profile. Please try again.', 'error');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Update profile display
        function updateProfileDisplay(data) {
            // Update header
            document.querySelector('.profile-name').textContent = `${data.first_name} ${data.last_name}`;
            document.querySelector('.profile-title').textContent = data.position || 'Business Professional';
            document.querySelector('.profile-company').innerHTML = `<i class="fas fa-building"></i> ${data.business_name || 'Company Name'}`;

            // Update personal info
            const personalInfo = document.getElementById('personalInfo');
            personalInfo.innerHTML = `
                <div class="info-item">
                    <span class="info-label">First Name</span>
                    <span class="info-value">${data.first_name || 'Not provided'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Last Name</span>
                    <span class="info-value">${data.last_name || 'Not provided'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email</span>
                    <span class="info-value">{{ client_data.work_email or 'Not provided' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Phone</span>
                    <span class="info-value">${data.mobile || 'Not provided'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Country</span>
                    <span class="info-value">${data.country || 'Not provided'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Birthday</span>
                    <span class="info-value">{{ client_data.birthday or 'Not provided' }}</span>
                </div>
            `;

            // Update business info
            const businessInfo = document.getElementById('businessInfo');
            businessInfo.innerHTML = `
                <div class="info-item">
                    <span class="info-label">Business Name</span>
                    <span class="info-value">${data.business_name || 'Not provided'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Industry</span>
                    <span class="info-value">${data.industry || 'Not provided'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Employee Count</span>
                    <span class="info-value">{{ client_data.employee_count or 'Not provided' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Business Address</span>
                    <span class="info-value">{{ client_data.business_address or 'Not provided' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Business Email</span>
                    <span class="info-value">{{ client_data.business_email or 'Not provided' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Website</span>
                    <span class="info-value">
                        ${data.business_website ?
                            `<a href="${data.business_website}" target="_blank" style="color: var(--primary-blue);">${data.business_website}</a>` :
                            'Not provided'
                        }
                    </span>
                </div>
            `;

            // Update about section
            const aboutInfo = document.getElementById('aboutInfo');
            aboutInfo.innerHTML = `
                <div class="info-item" style="flex-direction: column; align-items: flex-start;">
                    <span class="info-label" style="margin-bottom: 0.5rem;">Introduction</span>
                    <span class="info-value" style="text-align: left; line-height: 1.6;">
                        ${data.introduction || 'Tell us about yourself and your business...'}
                    </span>
                </div>
            `;
        }

        // Show notification
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                color: white;
                font-weight: 600;
                z-index: 3000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? 'var(--success)' : 'var(--error)'};
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Handle profile photo upload
        document.getElementById('profilePhotoInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const formData = new FormData();
                formData.append('profile_photo', file);

                fetch('/api/update-client-photo', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Force reload the image by adding a timestamp to bypass cache
                        const timestamp = new Date().getTime();
                        document.getElementById('profileImage').src = `/api/client-photo/{{ client_data.id }}?t=${timestamp}`;
                        showNotification('Profile photo updated successfully!', 'success');
                    } else {
                        showNotification('Error updating photo: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Error updating photo. Please try again.', 'error');
                });
            }
        });

        // Close modal when clicking outside
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditMode();
            }
        });

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
